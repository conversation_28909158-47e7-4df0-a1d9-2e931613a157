import router from "./router";
import { usePermissionStore } from "@/store/modules/permission";
import { useLoginStore } from "./store/modules/login";
// import store from './store'
import { ElMessage, ElLoading } from "element-plus";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import getPageTitle from "@/utils/get-page-title";

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = ["/login"]; // no redirect whitelist

// 页面跳转提示配置
const pageTransitionConfig = {
  useLoading: true,
  loadingOptions: {
    lock: true,
    text: "页面加载中...",
    background: "rgba(0, 0, 0, 0.7)"
  },
};

let loadingInstance = null;

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  
  if (pageTransitionConfig.useLoading) {
    loadingInstance = ElLoading.service(pageTransitionConfig.loadingOptions);
  }
  document.title = getPageTitle( to.meta.title );

  // determine whether the user has logged in
  const loginStore = useLoginStore();
  const hasToken = loginStore.token;
  const permissionStore = usePermissionStore();
  const isRouteLoaded = permissionStore.isRouteLoaded;

  if (hasToken) {
    if (to.path === "/login") {
      next({ path: "/" });
      
      NProgress.done();
    } else {
      if (isRouteLoaded) {
        next();
      } else {
        try {
          const dynamicRoutes = await permissionStore.generateRoutes(loginStore.permissions);
          
          dynamicRoutes.forEach((route) => router.addRoute(route));

          next({ ...to, replace: true });
        } catch (error) {
          // await store.dispatch('user/resetToken')
          ElMessage.error(error || "路由加载失败");

          next(`/login?redirect=${to.path}`);
          
          NProgress.done();
        }
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
  
  if (pageTransitionConfig.useLoading && loadingInstance) {
    loadingInstance.close();
  }
});