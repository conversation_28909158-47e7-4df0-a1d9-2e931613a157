<template>
  <div class="border-line">
    <div class="header">
      <h4>机器人状态变化时间线</h4>
      <div class="types">
        <div v-for="item in types" :key="item.name" class="type-item">
          <span class="type-icon" :style="{ background: item.color }" />
          <span class="type-name">{{ item.name }}</span>
        </div>
      </div>
    </div>
    <div ref="chartRef" style="width: 100%; height: 150px;" />
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { useEcharts } from "@/composables/useEcharts";
import { types } from "./bar.js";
import { getRobotStatusChange } from "@/api/robot.js";
import dayjs from "dayjs";

const props = defineProps({
  robotId: {
    type: String,
    default: ""
  }
});

const { chartRef, echarts, resize, setOptions } = useEcharts();

let data = [];
let categories = ["Status"];
function renderItem(params, api) {
  let categoryIndex = api.value(0);
  let start = api.coord([api.value(1), categoryIndex]);
  let end = api.coord([api.value(2), categoryIndex]);
  let height = api.size([0, 1])[1] * 0.6;
  let rectShape = echarts.graphic.clipRectByRect(
    {
      x: start[0],
      y: start[1] - height / 2,
      width: end[0] - start[0],
      height: height
    },
    {
      x: params.coordSys.x,
      y: params.coordSys.y,
      width: params.coordSys.width,
      height: params.coordSys.height
    }
  );
  
  return (
    rectShape && {
      type: "rect",
      transition: ["shape"],
      shape: rectShape,
      style: api.style()
    }
  );
}

const options = {
  tooltip: {
    formatter: function (params) {
      const startTime = dayjs(params.value[1]).format("YYYY-MM-DD HH:mm:ss");
      const endTime = dayjs(params.value[2]).format("YYYY-MM-DD HH:mm:ss");
      return `${params.marker}${params.name}<br/>
              开始时间：${startTime}<br/>
              结束时间：${endTime}`;
    }
  },
  dataZoom: [
    {
      type: "slider",
      filterMode: "weakFilter",
      showDataShadow: false,
      top: 100,
      height: 20,
      start: 0,
      end: 30,
      labelFormatter: (value) => {
        return dayjs(value).format("HH:mm:ss");
      }
    },
    {
      type: "inside",
      filterMode: "weakFilter"
    }
  ],
  grid: {
    top: 20,
    height: 50
  },
  xAxis: {
    type: "time",
    axisLabel: {
      formatter: (value) => {
        return dayjs(value).format("HH:mm:ss");
      }
    }
  },
  yAxis: {
    data: categories
  },
  series: [
    {
      type: "custom",
      renderItem: renderItem,
      itemStyle: {
        opacity: 0.8
      },
      encode: {
        x: [1, 2],
        y: 0
      },
      data: []
    }
  ]
};

const getBarData = async() => {
  const res = await getRobotStatusChange(props.robotId);

  if (res.code === 200) {
    data = [];
    
    res.data.stateInfos?.forEach((item) => {
      data.push({
        name: item.state,
        value: [
          0,
          new Date(item.stateStartTime).getTime(),
          new Date(item.stateEndTime).getTime(),
          item.state
        ],
        itemStyle: {
          color: types.find((type) => type.name === item.state).color
        }
      });
    });
    
    options.series[0].data = data;
    setOptions(options);
  }
};

onMounted(() => {
  getBarData();
});

const refreshChart = () => {
  // 刷新时重新获取数据并更新图表
  getBarData();
  resize();
};

defineExpose({
  refreshChart
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .types {
    font-size: 12px;
    display: flex;
    gap: 10px;
    margin-right: 20px;
    flex-wrap: wrap;
    
    .type-item {
      display: flex;
      align-items: center;

      .type-icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 5px;
        border-radius: 2px;
      }
    }

  }
}
</style>