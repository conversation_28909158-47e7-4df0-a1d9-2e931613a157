<template>
  <div class="screen-card time-ratio-card">
    <div class="card-header">
      <span class="card-title">各状态时间占比统计</span>
      <div class="time-filter">
        <el-radio-group v-model="timeRange" size="small">
          <el-radio-button value="0">
            天
          </el-radio-button>
          <el-radio-button value="1">
            周
          </el-radio-button>
          <el-radio-button value="3">
            月
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { useEcharts } from "@/composables/useEcharts";
import { getTimeRatioOptions } from "../config/chartOptions";
import { getStateTime } from "@/api/screen";

// 状态类型定义
const STATE_TYPES = {
  runAccumTime: { 
    name: "R<PERSON>", 
    itemStyle: { color: "#67C23A" }
  },
  idleAccumTime: {
    name: "IDLE", 
    itemStyle: { color: "#409EFF" }
  },
  stopAccumTime: {
    name: "STOP", 
    itemStyle: { color: "#E6A23C" }
  },
  errorAccumTime: { 
    name: "ERROR", 
    itemStyle: { color: "#F56C6C" }
  },
  unknownAccumTime: {
    name: "UNKNOWN", 
    itemStyle: { color: "#909399" }
  }
};

const timeRange = ref("0");
const options = getTimeRatioOptions();
const { chartRef, setOptions } = useEcharts();

// 监听时间范围变化
watch(timeRange, () => getRobotStateTime());

const getRobotStateTime = async () => {
  try {
    const { data } = await getStateTime({ periodType: Number(timeRange.value) });
    
    const chartData = Object.entries(STATE_TYPES).map(([key, config]) => ({
      name: config.name,
      value: Number(data[key] || 0).toFixed(0),
      itemStyle: config.itemStyle
    }));

    options.series[0].data = chartData;
    setOptions(options);
  } catch (error) {
    console.error("获取机器人状态时间失败:", error);
  }
};

onMounted(getRobotStateTime);
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>
