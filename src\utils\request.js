import axios from "axios";
import { ElMessage } from "element-plus";

// 创建 Axios 实例
const instance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_URL, // 你的 API 基础地址
  timeout: 50000, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

// 添加请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 在这里可以添加认证令牌或其他头部信息
    // 例如，如果用户登录，从本地存储中获取token
    const token = localStorage.getItem("login-token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    // 根据需要显示 loading 状态
    if (config.showLoading) {
      ElMessage.loading({ message: "请求中...", duration: 0 });
    }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    ElMessage.closeAll();
    return Promise.reject(error);
  }
);

// 添加响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 根据需要隐藏 loading 状态
    if (response.config.showLoading) {
      ElMessage.closeAll();
    }

    const res = response.data;

    if (res.code && res.code !== 200) {
      ElMessage({
        message: res.message || res.msg || "Error",
        type: "error",
        duration: 5 * 1000
      });

      return Promise.reject(new Error(res.message || "Error"));
    } 
    return res;
  },
  (error) => {
    if (error.response) {
      if (error.response.status === 400) {
        ElMessage.error("请求处理失败");
        return Promise.reject(error);
      }
      if (error.response.status === 401) {
        ElMessage.error("登录失效，请重新登录");
        window.location.href = "/login";
        return Promise.reject(error);
      }
      if (error.response.status === 404) {
        ElMessage.error("接口不存在");
        return Promise.reject(error);
      }
      if (error.response.status === 500) {
        ElMessage.error("服务器错误");
        return Promise.reject(error);
      }
      
      // 处理其他错误
      return error.response.data;
    } 
    if (error.code === "ERR_NETWORK") {
      ElMessage.error(error.code);
      return Promise.reject(error);
    }
    
    ElMessage.closeAll();
    return Promise.reject(error);
  }
);

export default instance;
