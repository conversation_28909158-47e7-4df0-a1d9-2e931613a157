<template>
  <div class="screen-container">
    <div class="screen-header">
      <div class="header-content">
        <div class="header-decoration left-decoration" />
        <h1 class="header-title">
          RPA 机器人监控大屏
        </h1>
        <div class="header-decoration right-decoration" />
      </div>
      <div class="header-time">
        {{ currentTime }}
      </div>
    </div>
    
    <div class="screen-content">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="8" class="screen-column mt-20">
          <robot-status />
          
          <time-ratio-chart />
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="8" class="screen-row-2 mt-20">
          <robot-list />
        </el-col>
 
        <el-col :xs="24" :sm="24" :md="8" class="screen-column mt-20">
          <task-trend-chart />
          
          <error-trend-chart />
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="screen-row">
        <el-col :xs="24" :sm="24" :md="8" class="screen-row mt-20">
          <robot-steps />
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" class="screen-row mt-20">
          <robot-activity />
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" class="screen-row mt-20">
          <robot-logs />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import RobotStatus from "./components/RobotStatus.vue";
import TimeRatioChart from "./components/TimeRatioChart.vue";
import TaskTrendChart from "./components/TaskTrendChart.vue";
import ErrorTrendChart from "./components/ErrorTrendChart.vue";
import RobotList from "./components/RobotList.vue";
import RobotSteps from "./components/RobotSteps.vue";
import RobotActivity from "./components/RobotActivity.vue";
import RobotLogs from "./components/RobotLogs.vue";


// 当前时间
const currentTime = ref(new Date().toLocaleString());
const timeInterval = setInterval(() => {
  currentTime.value = new Date().toLocaleString();
}, 1000);

// 组件卸载时清除定时器
onUnmounted(() => {
  clearInterval(timeInterval);
});
</script>

<style lang="scss" scoped>
.screen-container {
  width: 100%;
  height: 100vh;
  background-color: #1a2332;
  color: #fff;
  // padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.mt-20 {
  margin-top: 20px;
}

.screen-header {
  height: 60px;
  background: linear-gradient(180deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.8) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 20px;
  position: relative;
}

.header-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-decoration {
  width: 10vw;
  height: 2px;
  background: linear-gradient(90deg, rgba(59, 130, 246, 0) 0%, #3b82f6 50%, rgba(59, 130, 246, 0) 100%);
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(90deg, #60a5fa 0%, #3b82f6 100%);
  color: transparent;
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  white-space: nowrap;
}

.header-time {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 14px;
}

.screen-time {
  font-size: 16px;
  color: #bfcbd9;
}

.screen-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  //gap: 20px;
  overflow: auto;
}

.screen-row {
  /* display: flex;
  gap: 20px; */
  height: calc((100vh - 120px) / 3);
}

.screen-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
  height: calc((100vh - 120px) / 3 * 2)
}

.screen-row-2 {
  /* display: flex;
  gap: 20px; */
  height: calc((100vh - 120px) / 3 * 2) ;
}

:deep(.el-row) {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

:deep(.el-table) {
  background-color: transparent;
  color: #bfcbd9;
  
  .el-table__header-wrapper,
  .el-table__body-wrapper,
  .el-table__footer-wrapper {
    background-color: transparent;
  }
  
  th.el-table__cell {
    background-color: #1f2d3d;
    border-bottom: 1px solid #304156;
    color: #fff;
  }
  
  td.el-table__cell {
    border-bottom: 1px solid #304156;
  }
  
  tr.el-table__row:hover > td.el-table__cell {
    background-color: #263445 !important;
  }

  .el-table__inner-wrapper::before {
    background-color: #1f2d3d;
  }
}
  
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background-color: #263445;
}

:deep(.el-radio-button__inner) {
  background-color: #304156;
  border-color: #1f2d3d;
  color: #bfcbd9;
}

:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  box-shadow: -1px 0 0 0 #409eff;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-left: 1px solid #1f2d3d;
}

</style>