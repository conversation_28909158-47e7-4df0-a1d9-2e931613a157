<!-- DialogProvider.vue -->
<template>
  <el-dialog
    v-model="visible"
    v-bind="dialogProps"
  >
    <template
      v-if="$slots.header || headerContent"
      #header
    >
      <slot name="header" />
      <component
        :is="headerContent"
        v-if="headerContent && !$slots.header"
      />
    </template>
      
    <slot v-if="$slots.default && !content" />
    <component
      :is="content"
      v-else-if="content"
    />
      
    <template
      v-if="$slots.footer || footerContent"
      #footer
    >
      <slot name="footer" />
      <component
        :is="footerContent"
        v-if="footerContent && !$slots.footer"
      />
    </template>
  </el-dialog>
</template>
  
<script setup name="DialogProvider">
import { useDialog } from "@/composables/useDialog";

// 使用现有的useDialog钩子函数
const { 
  dialogProps,
  visible, 
  content, 
  headerContent, 
  footerContent,
  open,
  close,
  showLoading,
  hideLoading
} = useDialog();

// 向父组件暴露方法
defineExpose({
  open,
  close,
  showLoading,
  hideLoading
});
</script>

<style>
.el-dialog {
  display: flex !important;
  flex-direction: column !important;
  margin: 0 !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  max-height: 90vh !important;
  overflow: auto !important;
}
/* .el-dialog__body {
  padding: 0 10px !important;
  max-height: 80vh !important;
  overflow: auto !important;
} */
</style>
