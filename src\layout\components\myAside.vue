<template>
  <div class="menu-container">
    <template
      v-for="route in routes"
      :key="route.path"
    >
      <!-- 跳过hidden路由 -->
      <template v-if="!route.hidden">
        <!-- 无子路由或只有一个子路由且无redirect的情况 - 渲染为菜单项 -->
        <el-menu-item 
          v-if="!hasVisibleChildren(route)" 
          :index="getRoutePath(route)"
          @click="handleRoute(route)"
        >
          <icon-renderer :icon="route.meta?.icon" />
          <template #title>
            <span>{{ route.meta?.title }}</span>
          </template>
        </el-menu-item>

        <!-- 有可显示子路由的情况 - 渲染为子菜单 -->
        <el-sub-menu 
          v-else 
          :index="route.path"
          popper-class="sidebar-popper"
        >
          <template #title>
            <icon-renderer :icon="route.meta?.icon" />
            <span>{{ route.meta?.title }}</span>
          </template>
          
          <!-- 递归渲染子路由 -->
          <MyAside :main-router-list="route.children" />
        </el-sub-menu>
      </template>
    </template>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import IconRenderer from "./IconRenderer.vue";

const props = defineProps({
  mainRouterList: {
    type: Array,
    required: true
  }
});

const router = useRouter();

// 计算处理后的路由
const routes = computed(() => {
  return props.mainRouterList.map(route => {
    if (route.path === "/" && route.name === "Home" && route.children?.length > 0) {
      return {
        ...route,
        children: route.children.map(child => ({
          ...child,
          meta: child.meta || route.meta
        }))
      };
    }
    return route;
  });
});

const hasVisibleChildren = (route) => {
  if (!route.children || route.children.length === 0) {return false;}
  
  if (route.children.length === 1 && route.redirect === route.children[0].path) {
    return false;
  }
  
  return route.children.some(child => !child.hidden);
};

// 获取路由实际路径
const getRoutePath = (route) => {
  if (route.redirect && route.children && route.children.length > 0) {
    return route.children[0].path;
  }
  
  return route.path;
};

// 处理路由点击
const handleRoute = (route) => {
  // 外部链接处理
  if (route.meta?.isLink) {
    const url = typeof route.meta.isLink === "string" ? route.meta.isLink : route.path;
    // 检查URL是否包含协议
    const fullUrl = url.startsWith("http") ? url : `https://${url}`;
    window.open(fullUrl, "_blank");
    return;
  }
  
  // 处理重定向路由
  if (route.redirect && route.children && route.children.length > 0) {
    router.push(route.children[0].path);
    return;
  }
  
  // 正常路由跳转
  const fullPath = route.path.startsWith("/") ? route.path : `/${route.path}`;
  router.push(fullPath);
};
</script>

<style scoped>
.menu-container {
  width: 100%;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  display: flex;
  align-items: center;
}

:deep(.el-menu-item) {
  display: flex;
  align-items: center;
}
</style>
