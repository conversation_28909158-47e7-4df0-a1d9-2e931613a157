// stores/login.js
import { defineStore } from "pinia";
import { useLocalStorage } from "@vueuse/core";
import { computed } from "vue";

export const useLoginStore = defineStore("login", () => {
  // 使用 useLocalStorage 存储用户信息，默认为 null
  const user = useLocalStorage("login-user", null);
  const userId = useLocalStorage("login-userId", null);
  // 使用 useLocalStorage 存储 token，默认为空字符串
  const token = useLocalStorage("login-token", "");
  
  // 其他可能需要持久化的登录信息
  const permissions = useLocalStorage("login-menu", []);
  const settings = useLocalStorage("login-settings", {});
  
  const isLoggedIn = computed(() => !!token.value);
  
  function login(info) {
    user.value = info.userName;
    userId.value = info.id;
    token.value = info.id;
    permissions.value = info.menus;
  }
  
  // 清除所有 localStorage 和 cookie
  function logout() {
    // 1. 清除 useLocalStorage 管理的项
    user.value = null;
    token.value = "";
    permissions.value = [];
    settings.value = {};
    
    // 2. 清除其他所有 localStorage 项
    localStorage.clear();
    
    // 3. 清除所有 cookies
    const cookies = document.cookie.split(";");
    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i];
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();
      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
    }
  }
  
  return {
    user,
    userId,
    token,
    permissions,
    settings,
    isLoggedIn,
    login,
    logout
  };
});