<template>
  <div
    v-if="_paginationConfig.total > 0"
    class="pagination-wrapper"
    style="display: flex; justify-content: flex-end;"
  >
    <el-pagination
      v-bind="_paginationConfig"
      @size-change="pageSizeChange"
      @current-change="currentPageChange"
    />
  </div>
</template>

<script setup>
const props = defineProps( {
  pagination: {
    type: Object,
    default: () => {return {};}
  }
});

const emits = defineEmits([
  "size-change",
  "current-change", 
]); 

const _paginationConfig = computed(() => {
  const config = {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 30, 40, 50],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  };
  
  return Object.assign(config, props.pagination);
});

// 切换pageSize
const pageSizeChange = (pageSize) => {
  emits("size-change", pageSize);
};

// 切换currentPage
const currentPageChange = (currentPage) => {
  emits("current-change", currentPage);
};
</script>

<style lang="scss" scoped>

</style>