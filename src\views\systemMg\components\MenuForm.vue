<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="500px"
    @update:model-value="$emit('update:visible', $event)"
  >
    <EasyForm
      ref="easyFormRef"
      :filed-list="formFields"
      :form-data="formData"
      :options="formOptions"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
  </el-dialog>
</template>

<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: "新增菜单"
  },
  menuData: {
    type: Object,
    default: () => ({
      parentId: "",
      name: "",
      url: "",
      no: "",
      icon: "",
      isVisible: true,
      isOutSide: false,
      isLink: ""
    })
  }
});

const emit = defineEmits(["update:visible", "submit", "cancel"]);

// 表单字段配置
const formFields = computed(() => {
  const fields = [
    {
      label: "菜单名称",
      prop: "name",
      type: "input",
      placeholder: "请输入菜单名称",
      rules: [{ required: true, message: "请输入菜单名称", trigger: "blur" }]
    },
    {
      label: "路由路径",
      prop: "url",
      type: "input",
      placeholder: "请输入路由路径",
      rules: [{ required: true, message: "请输入路由路径", trigger: "blur" }]
    },
    {
      label: "图标",
      prop: "icon",
      type: "input",
      placeholder: "请输入图标",
      rules: [{ required: true, message: "请输入图标", trigger: "blur" }]
    },
    {
      label: "序号",
      prop: "no",
      type: "input",
      inputType: "number",
      minLength: 1,
      rules: [{ required: true, message: "请输入序号", trigger: "blur" }]
    },
    {
      label: "是否显示",
      prop: "isVisible",
      type: "radio",
      radioList: [
        { label: "显示", value: true },
        { label: "隐藏", value: false }
      ]
    },
    {
      label: "外部链接",
      prop: "isOutSide",
      type: "radio",
      radioList: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    }
  ];

  if (formData.value.isOutSide) {
    fields.push({
      label: "链接地址",
      prop: "isLink",
      type: "input",
      placeholder: "请输入链接地址",
      rules: [{ required: true, message: "请输入链接地址", trigger: "blur" }]
    });
  }

  return fields;
});

// 表单配置
const formOptions = {
  justify: "center",
  labelWidth: "100px",
  showResetButton: false,
  submitButtonText: "确 定",
  cancelButtonText: "取 消"
};

// 表单数据
const formData = ref({
  ...props.menuData,
  isOutSide: props.menuData.isOutSide ?? false
});

// 监听菜单数据变化
watch(() => props.menuData, (val) => {
  Object.assign(formData.value, val);
}, { immediate: true, deep: true });

// 提交表单
const handleSubmit = (data) => {
  emit("submit", data);
  emit("update:visible", false);
};

// 取消
const handleCancel = () => {
  emit("update:visible", false);
  emit("cancel");
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>