const formFiled = [
  {
    type: "input",
    label: "关键词",
    prop: "keywords"
  }
];

const formOption = {
  justify: "center",
  labelWidth: "60px",
  inline: true,
  labelPosition: "right",
  submitButtonText: "查询",
  showCancelButton: false
};

const formData = {
  keywords: ""
};

const tableCol_set = [
  { prop: "userName", label: "用户名" },
  { prop: "phoneNumber", label: "手机号" },
  { prop: "userEmail", label: "邮箱" },
  { prop: "rolesName", label: "用户组群" },
  /* { prop: "creationTime", label: "创建时间", type: "date" }, */
  {
    width: "200",
    label: "操作",
    buttons: [
      { name: "编辑", command: "edit",
        show: (row) => row.status !== 1,
        //disabled: (row) => row.isLocked 
      },
      { name: "修改密码", type: "info", command: "psw" },
      { name: "删除", type: "danger", command: "delete" }
    ]
  }
];

const tableCol_group = [
  { prop: "name", label: "用户组名" },
  { prop: "creationTime", label: "创建时间", type: "date", width: "150" },
  {
    width: "200",
    label: "操作",
    buttons: [
      /* { name: "编辑", command: "edit",
        show: (row) => row.status !== 1,
        //disabled: (row) => row.isLocked 
      }, */
      { name: "绑定权限", type: "primary", command: "menu" },
      { name: "删除", type: "danger", command: "delete" }
    ]
  }
];

// 将平级数组转换为树形结构
const arrayToTree = (items) => {
  const result = [];
  const itemMap = {};  

  for (const item of items) {
    itemMap[item.id] = {
      ...item,
      children: []
    };
  }
  
  for (const item of items) {
    const id = item.id;
    const pid = item.parentId;
    const treeItem = itemMap[id];
    
    if (pid === 0 || !pid) {
      result.push(treeItem);
    } else {
      if (itemMap[pid]) {
        itemMap[pid].children.push(treeItem);
      }
    }
  }

  const sortTreeNodes = (nodes) => {
    nodes.sort((a, b) => {
      if (!a.no && !b.no) {return 0;}
      if (!a.no) {return 1;}
      if (!b.no) {return -1;}
      return Number(a.no) - Number(b.no);
    });
    // 递归排序子节点
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        sortTreeNodes(node.children);
      }
    });
    return nodes;
  };

  return sortTreeNodes(result);
};

export default {
  formFiled,
  formOption,
  formData,
  tableCol_set,
  tableCol_group,
  arrayToTree
};

