import { ElTag } from "element-plus";

const formFiled = [
  {
    type: "input",
    label: "关键词",
    prop: "keywords"
  },
  {
    type: "daterange",
    label: "时间",
    prop: "dateRange",
  }
];

const formOption = {
  justify: "center",
  labelWidth: "60px",
  inline: true,
  labelPosition: "right",
  submitButtonText: "查询",
  showCancelButton: false
};

const formData = {
  keywords: "",
  dateRange: ""
};

const tableCol = [
  { prop: "robotId", label: "机器人编号", minWidth: "150" },
  { prop: "robotName", label: "机器人名称", minWidth: "150" },
  { prop: "eqpId", label: "设备编号", minWidth: "150" },
  { prop: "eqpName", label: "设备名称", minWidth: "150" },
  { prop: "kvmIp", label: "KVM IP", minWidth: "150" },
  { prop: "kvmPort", label: "KVM Port", minWidth: "120" },
  { prop: "isActive", label: "激活状态", width: "100", render: ({ row }) => 
    h(ElTag, { type: row.isActive ? "success" : "info", effect: "dark" }, 
      () => row.isActive ? "激活" : "未激活") },
  { prop: "notes", label: "备注信息" },
  { prop: "createTime", label: "创建时间", type: "date", width: "150" },
  { prop: "updateTime", label: "更新时间", type: "date", width: "150" },
  {
    width: "130",
    label: "操作",
    fixed: "right",
    buttons: [
      { name: "编辑", command: "edit",
        show: (row) => row.status !== 1,
        //disabled: (row) => row.isLocked 
      },
      { name: "删除", type: "danger", command: "delete" }
    ]
  }
];

export default {
  formFiled,
  formOption,
  formData,
  tableCol
};