import request from "@/utils/request";
// 剧本设置
export function addScript(data) {
  return request({
    url: "/api/app/script/script",
    method: "post",
    data
  });
}

export function editScript(data) {
  return request({
    url: "/api/app/script/edit-script",
    method: "post",
    data
  });
}

export function deleteScript(id) {
  return request({
    url: "/api/app/script/script/" + id,
    method: "delete"
  });
}

// 剧本组设置
export function getScriptGroupList(params) {
  return request({
    url: "/api/app/script/script-group-list",
    method: "get",
    params
  });
}

export function addScriptGroup(data) {
  return request({
    url: "/api/app/script/script-group",
    method: "post",
    data
  });
}

export function editScriptGroup(data) {
  return request({
    url: "/api/app/script/edit-script-group",
    method: "post",
    data
  });
}

export function deleteScriptGroup(id) {
  return request({
    url: "/api/app/script/script-group/" + id,
    method: "delete"
  });
}

export function bindUserGroup(data) {
  return request({
    url: "/api/app/script/set-script-group-auth",
    method: "post",
    data
  });
}
