import request from "@/utils/request";
/* 菜单设置 */
export function getMenu(params) {
  return request({
    url: "/api/app/menu/menu-list",
    method: "get",
    params,
  });
}

export function deleteMenu(params) {
  return request({
    url: "/api/app/menu/menu",
    method: "delete",
    params,
  });
}

export function addMenu(data) {
  return request({
    url: "/api/app/menu/menu",
    method: "post",
    data,
  });
}

export function editMenu(data) {
  return request({
    url: "/api/app/menu/edit-menu",
    method: "post",
    data,
  });
}

/* 用户组设置 */
export function getUserGroup(params) {
  return request({
    url: "/api/app/user-group/user-groups",
    method: "get",
    params,
  });
}

export function addUserGroup(data) {
  return request({
    url: "/api/app/user-group",
    method: "post",
    data,
  });
}

export function editUserGroup(id, data) {
  return request({
    url: "/api/app/user-group/" + id,
    method: "put",
    data,
  });
}

export function deleteUserGroup(id) {
  return request({
    url: "/api/app/user-group/"+ id,
    method: "delete",
  });
}

export function getGroupBindMenu(id) {
  return request({
    url: "/api/app/authority/role-menu-list/" + id,
    method: "get",
  });
}

export function bindMenu(data) {
  return request({
    url: "/api/app/authority/set-role-menus",
    method: "post",
    data,
  });
}

/* 用户设置 */
export function addUser(data) {
  return request({
    url: "/api/app/user",
    method: "post",
    data,
  });
}

export function editUser(id, data) {
  return request({
    url: "/api/app/user/" + id,
    method: "put",
    data,
  });
}

export function deleteUser(id) {
  return request({
    url: "/api/app/user/"+ id,
    method: "delete",
  });
}

export function changeUserPsw(id, params) {
  return request({
    url: "/api/app/user/" + id +"/psw-change",
    method: "post",
    params,
  });
}