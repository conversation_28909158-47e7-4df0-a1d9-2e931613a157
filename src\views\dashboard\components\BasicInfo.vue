<template>
  <div class="border-line">
    <h4>基本信息</h4>
    <el-divider />
    <el-row :gutter="20">
      <el-col v-for="item in basicInfoItems" :key="item.label" :xs="24" :sm="12" :md="6">
        <el-form-item :label="item.label">
          <span class="form-value">{{ item.value }}</span>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <el-form-item label="激活状态:">
          <el-tag :type="robotInfo.isActive ? 'success' : 'info'">
            {{ robotInfo.isActive ? "激活" : "未激活" }}
          </el-tag>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col v-for="item in timeItems" :key="item.label" :xs="24" :sm="12" :md="6">
        <el-form-item :label="item.label">
          <span class="form-value">{{ dayjs(item.value).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12">
        <el-form-item label="备注:">
          <span class="form-value">{{ robotInfo.notes || '-' }}</span>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import dayjs from "dayjs";

const props = defineProps({
  robotInfo: {
    type: Object,
    required: true
  }
});

const basicInfoItems = computed(() => [
  { label: "机器人编号:", value: props.robotInfo.robotId },
  { label: "机器人名称:", value: props.robotInfo.robotName },
  { label: "设备编号:", value: props.robotInfo.eqpId },
  { label: "设备名称:", value: props.robotInfo.eqpName },
  { label: "KVM IP:", value: props.robotInfo.kvmIp },
  { label: "KVM Port:", value: props.robotInfo.kvmPort }
]);

const timeItems = computed(() => [
  { label: "创建时间:", value: props.robotInfo.createTime },
  { label: "更新时间:", value: props.robotInfo.updateTime }
]);
</script>

<style lang="scss" scoped>
@import url(./index.scss);
</style>
