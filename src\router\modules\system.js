import Layout from "@/layout/index.vue";

export default {
  path: "/systemMg",
  name: "systemMg",
  meta: { icon: "Setting", title: "系统管理" },
  redirect: "/systemMg/userSet",
  component: Layout,
  children: [
    {
      path: "/systemMg/userSet",
      name: "userSet",
      meta: { icon: "svg-userset", title: "用户设置" },
      component: () => import("@/views/systemMg/userSet.vue"),
    },
    {
      path: "/systemMg/userGroup",
      name: "userGroup",
      meta: { icon: "svg-usergroup", title: "用户组设置" },
      component: () => import("@/views/systemMg/userGroup.vue"),
    },
    {
      path: "/systemMg/menu",
      name: "menu",
      meta: { icon: "svg-menu", title: "菜单设置" },
      component: () => import("@/views/systemMg/menu.vue"),
    }
  ],
};
