<template>
  <div class="view-dialog">
    <dialog-provider ref="formDialogRef">
      <el-form :model="dialogData" label-width="100px" label-position="left" class="view-form">
        <div class="form-content">
          <BasicInfo :robot-info="dialogData" />
          <RealTimeInfo :robot-info="dialogData" />
        </div>
      </el-form>
      <div>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="12">
            <LogTable
              ref="stateLogRef"
              title="机器人状态日志"
              :table-data="stateLogData"
              :total="stateLogTotal"
              :current-page="stateLogPage"
              @update:current-page="handleStatePageChange"
              @navigate="goStatusLog"
              @time-change="handleStateTimeChange"
            >
              <el-table-column prop="robotState" label="状态" align="center">
                <template #default="{ row }">
                  <el-tag 
                    :type="stateMap[row.robotState]?.type || 'info'"
                    effect="dark"
                    size="small"
                  >
                    {{ stateMap[row.robotState]?.label || "未知" }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="stateUpdateTime" label="更新时间" align="center">
                <template #default="scope">
                  {{ dayjs(scope.row.stateUpdateTime).format('YYYY-MM-DD HH:mm:ss') }}
                </template>
              </el-table-column>
            </LogTable>
          </el-col>
          <el-col :xs="24" :sm="24" :md="12">
            <LogTable
              ref="stepLogRef"
              title="运行步骤日志"
              :table-data="stepLogData"
              :total="stepLogTotal"
              :current-page="stepLogPage"
              @update:current-page="handleStepPageChange"
              @navigate="goStepLog"
              @time-change="handleStepTimeChange"
            >
              <el-table-column prop="robotStep" label="执行步骤" align="center" />
              <el-table-column prop="createTime" label="更新时间" align="center">
                <template #default="scope">
                  {{ dayjs(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
                </template>
              </el-table-column>
            </LogTable>
          </el-col>
        </el-row>
      </div>
      <StatusBar ref="statusBarRef" :robot-id="dialogData.robotId" />
      <div class="form-footer">
        <el-button type="primary" @click="sureForm">
          确定
        </el-button>
      </div>
    </dialog-provider>
  </div>
</template>

<script setup>
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { getStepLog, getStateLog } from "@/api/log";
import BasicInfo from "./BasicInfo.vue";
import RealTimeInfo from "./RealTimeInfo.vue";
import LogTable from "./LogTable.vue";
import StatusBar from "./StatusBar.vue";

const stateMap = {
  ERROR: { type: "danger", label: "ERROR" },
  RUN: { type: "success", label: "RUN" },
  IDLE: { type: "primary", label: "IDLE" },
  STOP: { type: "warning", label: "STOP" },
  UNKNOWN: { type: "info", label: "UNKNOWN" }
};

const props = defineProps({
  dialogData: {
    type: Object,
    default: () => ({})
  }
});

const route = useRouter();
const formDialogRef = ref(null);
const statusBarRef = ref(null);  // 添加ref

// 分别管理状态日志和步骤日志的数据
const stateLogData = ref([]);
const stateLogTotal = ref(0);
const stateLogPage = ref(1);
const stateLogRef = ref(null);
const stateDate = ref(null);

const stepLogData = ref([]);
const stepLogTotal = ref(0);
const stepLogPage = ref(1);
const stepLogRef = ref(null);
const stepDate = ref(null);

// 获取状态日志数据
const getStateLogData = () => {
  getStateLog({
    startTime: stateDate.value?.startTime,
    endTime: stateDate.value?.endTime,
    keywords: props.dialogData.robotId,
    skipCount: (stateLogPage.value - 1) * 10,
    maxResultCount: 10
  }).then((res) => {
    stateLogData.value = res.data.items;
    stateLogTotal.value = res.data.totalCount;
  });
};

// 获取步骤日志数据
const getStepLogData = () => {
  getStepLog({
    startTime: stepDate.value?.startTime,
    endTime: stepDate.value?.endTime,
    keywords: props.dialogData.robotId,
    skipCount: (stepLogPage.value - 1) * 10,
    maxResultCount: 10
  }).then((res) => {
    stepLogData.value = res.data.items;
    stepLogTotal.value = res.data.totalCount;
  });
};
// 状态数据时间切换
const handleStateTimeChange = (val) => {
  stateLogPage.value = 1;
  stateDate.value = val;
  getStateLogData();
};
// 步骤数据时间切换
const handleStepTimeChange = (val) => {
  stepLogPage.value = 1;
  stepDate.value = val;
  getStepLogData();
};
// 状态数据页码切换
const handleStatePageChange = (val) => {
  stateLogPage.value = val;
  getStateLogData();
};
// 步骤数据页码切换
const handleStepPageChange = (val) => {
  stepLogPage.value = val;
  getStepLogData();
};

const openFormDialog = () => {
  stateLogPage.value = 1;
  stepLogPage.value = 1;
  formDialogRef.value?.open({
    headerContent: h("div", 
      {style: "font-weight: 600;"},
      "机器人详情 ", 
      h("span", 
        {style: "font-size: 14px;font-weight: 500;"}, 
        " [ " + props.dialogData.robotName + " ]")),
    width: "80%",
    closeOnClickModal: false
  });
  
  // 使用nextTick确保组件已挂载后再调用方法
  nextTick(() => {
    stateLogRef.value?.handleTimeChange("1");
    stepLogRef.value?.handleTimeChange("1");

    setTimeout(() => {
      statusBarRef.value?.refreshChart();
    }, 300);
  });
};

const sureForm = () => {
  formDialogRef.value.close();
};

const goStatusLog = () => {
  route.push({
    path: "/log/robot",
    query: {
      keywords: props.dialogData.robotId
    }
  });
};

const goStepLog = () => {
  route.push({
    path: "/log/step",
    query: {
      keywords: props.dialogData.robotId
    }
  });
};

defineExpose({
  openFormDialog
});
</script>

<style lang="scss" scoped>
.form-footer {
  text-align: center;
}

:deep(.el-tag--dark){
   &.el-tag--primary{
     background-color: #fadb14;
     border-color: #fadb14;
   }

   &.el-tag--success{
     background-color: #52c41a;
     border-color: #52c41a;
   }

   &.el-tag--danger{
     background-color: #f5222d;
     border-color: #f5222d;
   }

   &.el-tag--warning{
     background-color: #00fefe;
     border-color: #00fefe;
   }

   &.el-tag--info{
     background-color: #a5a5a5;
     border-color: #a5a5a5;
   }

}
</style>