{"name": "vite-rpa-admin", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build:dev": "vite build --mode dev", "build:prod": "vite build --mode prod", "preview": "vite preview", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src"}, "dependencies": {"@microsoft/signalr": "^8.0.7", "@vueuse/core": "^13.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.8", "fast-glob": "^3.3.3", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "path-to-regexp": "^8.2.0", "pinia": "^3.0.2", "sass": "^1.87.0", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^5.2.2", "eslint": "^8.57.0", "eslint-config-vue-global-api": "^0.4.1", "eslint-plugin-vue": "^9.24.0", "terser": "^5.39.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.1", "vite-plugin-eslint": "^1.8.1"}}