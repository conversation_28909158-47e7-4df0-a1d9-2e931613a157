import * as echarts from "echarts";

// chartOptions.js
export const getTaskChartOptions = () => ({
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" }
  },
  grid: {
    top: "15px",
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true
  },
  xAxis: [
    {
      type: "category",
      data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
      axisTick: { alignWithLabel: true },
      axisLine: { lineStyle: { color: "#bfcbd9" } },
      axisLabel: { 
        color: "#bfcbd9",
        interval: 1,
        rotate: 25
      }
    }
  ],
  yAxis: [
    {
      type: "value",
      axisLine: { lineStyle: { color: "#bfcbd9" } },
      axisLabel: { color: "#bfcbd9" },
      splitLine: { lineStyle: { color: "#304156" } }
    }
  ],
  series: [
    {
      name: "任务量",
      type: "bar",
      barWidth: "60%",
      data: [10, 15, 12, 8, 7, 9, 14],
      itemStyle: { color: "#409eff" }
    }
  ]
});

export const getTimeRatioOptions = () => ({
  tooltip: { 
    trigger: "item",
    formatter: function(params) {
      const color = params.color;
      return `${params.seriesName}<br/>
              <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};"></span> 
              ${params.name} ${params.percent}%`;
    }
  },
  legend: {
    orient: "horizontal",
    bottom: "bottom",
    textStyle: { color: "#bfcbd9" }
  },
  series: [
    {
      name: "时间占比",
      type: "pie",
      radius: ["40%", "70%"],
      center: ["50%", "40%"],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#304156",
        borderWidth: 2
      },
      label: {
        show: false,
        position: "center"
      },
      emphasis: {
        label: {
          show: true,
          fontSize: "16",
          fontWeight: "bold",
          formatter: "{b}\n{d}%"
        }
      },
      labelLine: { show: false },
      data: [
        /* { value: 45, name: "RUN", itemStyle: { color: "#67C23A" } },
        { value: 25, name: "IDLE", itemStyle: { color: "#409EFF" } },
        { value: 15, name: "STOP", itemStyle: { color: "#E6A23C" } },
        { value: 10, name: "ERROR", itemStyle: { color: "#F56C6C" } },
        { value: 5, name: "UNKNOWN", itemStyle: { color: "#909399" } } */
      ]
    }
  ]
});

export const getActivityOptions = () => ({
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" }
  },
  grid: {
    top: "15px",
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true
  },
  xAxis: {
    type: "category",
    data: [],
    axisLine: { lineStyle: { color: "#bfcbd9" } },
    axisLabel: { color: "#bfcbd9", interval: 1, rotate: 25 }
  },
  yAxis: {
    type: "value",
    axisLine: { lineStyle: { color: "#bfcbd9" } },
    axisLabel: { color: "#bfcbd9" },
    splitLine: { lineStyle: { color: "#304156" } }
  },
  series: [
    {
      name: "活跃机器人",
      data: [],
      type: "bar",
      stack: "total",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#83bff6" },
          { offset: 0.5, color: "#409eff" },
          { offset: 1, color: "#188df0" }
        ])
      },
    },
    {
      name: "非活跃机器人",
      type: "bar",
      stack: "total",
      itemStyle: {
        color: "rgba(255, 255, 255, 0.1)"
      },
      data: []
    }
  ]
});

export const getErrorOptions = () => ({
  tooltip: { trigger: "axis" },
  grid: {
    top: "15px",
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: [],
    axisLine: { lineStyle: { color: "#bfcbd9" } },
    axisLabel: { color: "#bfcbd9", interval: 1, rotate: 25 }
  },
  yAxis: {
    type: "value",
    axisLine: { lineStyle: { color: "#bfcbd9" } },
    axisLabel: { color: "#bfcbd9" },
    splitLine: { lineStyle: { color: "#304156" } }
  },
  series: [
    {
      name: "故障数",
      data: [],
      type: "line",
      smooth: true,
      lineStyle: { color: "#F56C6C" },
      itemStyle: { color: "#F56C6C" },
      areaStyle: {
        color: {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: "rgba(245, 108, 108, 0.5)" },
            { offset: 1, color: "rgba(245, 108, 108, 0.1)" }
          ]
        }
      }
    }
  ]
});