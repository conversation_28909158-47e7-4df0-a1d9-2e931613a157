.tabs-box {
  background-color: var(--el-bg-color);
}
.tabs-box .tabs-menu {
  position: relative;
  width: 100%;
}
.tabs-box .tabs-menu .el-dropdown {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
}
.tabs-box .tabs-menu .el-dropdown .more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 43px;
  cursor: pointer;
  border-left: 1px solid var(--el-border-color-light);
  transition: all 0.3s;
}
.tabs-box .tabs-menu .el-dropdown .more-button:hover {
  background-color: var(--el-color-info-light-9);
}
.tabs-box .tabs-menu .el-dropdown .more-button .iconfont {
  font-size: 12.5px;
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header {
  box-sizing: border-box;
  height: 40px;
  padding: 0 10px;
  margin: 0;
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header .el-tabs__nav-wrap {
  position: absolute;
  width: calc(100% - 70px);
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav {
  display: flex;
  border: none;
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav .el-tabs__item {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #afafaf;
  border: none;
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav .el-tabs__item .tabs-icon {
  margin: 1.5px 4px 0 0;
  font-size: 15px;
  color: var(--el-color-primary);
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav .el-tabs__item .is-icon-close {
  margin-top: 1px;
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav .el-tabs__item.is-active {
  color: var(--el-color-primary);
}
.tabs-box .tabs-menu :deep(.el-tabs) .el-tabs__header .el-tabs__nav-wrap .el-tabs__nav .el-tabs__item.is-active::before {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 0;
  content: "";
  border-bottom: 2px solid var(--el-color-primary) !important;
}/*# sourceMappingURL=index.css.map */