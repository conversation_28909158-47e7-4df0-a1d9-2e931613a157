
@use "./variables.scss";
@use "./mixin.scss";
@use "./transition.scss";
@use "./sidebar.scss";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Microsoft YaHei,
    Arial,
    sans-serif;
  margin: 0;
  padding: 0;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}


:root {
  --color: #4C8BF5
}

#app {
  height: 100%;
  overflow: hidden;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  // height: calc(100vh - 90px);
  // overflow-y: auto;
  padding: 20px;
  background-color: #fff;
  margin: 20px;
}

.el-table tr, .el-form-item__label {
  color: #333;
  font-size: 13px;
}