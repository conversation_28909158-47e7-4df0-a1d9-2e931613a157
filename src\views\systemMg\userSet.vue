<template>
  <div class="app-container">
    <EasyForm
      style="text-align: center;"
      :filed-list="formFileds"
      :form-data="formDatas"
      :options="formOptions"
      @submit="searchFn"
      @reset="reset"
    />
    <div class="op-wrapper" style="margin-bottom: 20px;">
      <el-button type="primary" @click="addScript">
        增加
      </el-button>
    </div>
    <EasyTable
      :loading="loading" 
      :columns="tableColumn" 
      :table-data="listData" 
      :options="{ showPagination: true }" 
      :pagination="pagination" 
      @command="TableAction" 
      @size-change="handleSizeChange" 
      @current-change="handleCurrentChange"
    />
    <FormDialog
      ref="fromDialog"
      :dialog-type="scriptFormType"
      :dialog-data="scriptFormData"
      @refresh="search"
    />
    <ChangePsw 
      ref="changePswDialog"
      :dialog-data="pswFormData"
      @refresh="search"
    />
  </div>
</template>

<script setup>
import { useTable } from "@/composables/useTable.js";
import listDefine from "./listDefine";
import FormDialog from "./components/userSetDialog.vue";
import { useLoginStore } from "@/store/modules/login";
import { ElMessage, ElMessageBox } from "element-plus";
import ChangePsw from "./components/ChangePsw.vue";
import { deleteUser } from "@/api/systemMg.js";

const formFileds = listDefine.formFiled;
const formOptions = listDefine.formOption;
const formDatas = ref(listDefine.formData);
const fromDialog = ref(null);
const scriptFormType = ref("add");
const scriptFormData = ref({});
const changePswDialog = ref(null);
const pswFormData = ref({});

const tableColumn = listDefine.tableCol_set;
const listData = ref([]);

const commonFn = inject("$commonUtils"); // 注入公共方法
const loginStore = useLoginStore();

const { 
  searchParam, 
  pagination, 
  tableData, 
  loading, 
  search, 
  reset, 
  handleSizeChange, 
  handleCurrentChange 
} = useTable({
  api: "/api/app/user/user-list",
  isPageable: true,
  manual: true,
  paramMapping: {
    current: "skipCount",
    pageSize: "maxResultCount"
  }
});

watch(tableData, (newValue) => {
  newValue.map((item) => {
    item.rolesName = item.roles?.map((role) => role.roleName).join(",");
  });
  listData.value = newValue;
}, { immediate: true });

const TableAction = (command, row) => {
  switch (command) {
  case "edit":
    scriptFormType.value = "edit";
    scriptFormData.value = {
      id: row.id,
      userName: row.userName,
      phoneNumber: row.phoneNumber,
      email: row.userEmail,
      isActive: row.isActive,
      roleNames: row.roles?.map(role => role.roleName),
      rolesData: row.roles
    };
    nextTick(() => {
      fromDialog.value.openFormDialog();
    });
    break;
  case "psw":
    changePswDialog.value.openFormDialog(row.id);
    break;
  case "delete":
    if(loginStore.userId === row.id) {
      ElMessage.error("不能删除自己！");
      return;
    }
    ElMessageBox.confirm("是否确认删除?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }).then(async () => {
      try {
        const res = await deleteUser(row.id);
        if (res) {
          ElMessage.success("删除成功");
          search();
        } else {
          ElMessage.error("删除失败");
        }
      } catch (error) {
        ElMessage.error("删除失败: " + (error.msg || "未知错误"));
      }
    });
    break;
  default:
    break;
  }
};

const searchFn = () => {
  if(formDatas.value.dateRange?.length > 0) {
    formDatas.value.dateRange = commonFn.formatDateRange(formDatas.value.dateRange);
  }
  searchParam.value = { ...formDatas.value };
  search();
};

const addScript = () => {
  scriptFormType.value = "add";
  scriptFormData.value = {};
  nextTick(() => {
    fromDialog.value.openFormDialog();
  });
};
</script>

<style lang="scss" scoped>

</style>