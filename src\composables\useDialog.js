// useDialog.js - 修复后的代码
// eslint-disable-next-line no-unused-vars
import { ref, computed, h, render } from "vue";
// eslint-disable-next-line no-unused-vars
import { ElDialog, ElLoading } from "element-plus";

export function useDialog() {
  // 控制弹窗显示隐藏
  const visible = ref(false);
  // 弹窗标题
  const title = ref("");
  // 弹窗宽度
  const width = ref("50%");
  // 弹窗内容
  const content = ref(null);
  // 自定义顶部插槽内容
  const headerContent = ref(null);
  // 自定义底部插槽内容
  const footerContent = ref(null);
  // 是否显示关闭按钮
  const showClose = ref(true);
  // 是否显示遮罩层
  const modal = ref(true);
  // 是否在点击遮罩层时关闭弹窗
  const closeOnClickModal = ref(true);
  // 弹窗关闭前的回调
  const beforeCloseCallback = ref(null);
  // 弹窗挂载的DOM元素
  const appendToBody = ref(true);
  // 弹窗关闭动画
  const destroyOnClose = ref(false);
  // 弹窗是否全屏
  const fullscreen = ref(false);
  // 是否显示loading状态
  const loading = ref(false);
  
  // loading实例
  let loadingInstance = null;
  
  // 打开弹窗的方法
  const open = (options = {}) => {
    // eslint-disable-next-line no-unused-vars
    /* Object.entries(options).forEach(([key, value]) => {
          // 根据key来设置对应的ref值
          if (key in {
            content: true,
            title: true,
            width: true,
            headerContent: true,
            footerContent: true,
            showClose: true,
            modal: true,
            closeOnClickModal: true,
            beforeCloseCallback: true,
            appendToBody: true,
            destroyOnClose: true,
            fullscreen: true
          }) {
            // 使用这种方式直接设置ref的值
            eval(`${key}.value = value`);
          }
        }); */
    
    // 使用简单的if判断代替eval
    if (options.content) {content.value = options.content;}
    if (options.title) {title.value = options.title;}
    if (options.width) {width.value = options.width;}
    if (options.headerContent) {headerContent.value = options.headerContent;}
    if (options.footerContent) {footerContent.value = options.footerContent;}
    if (options.showClose !== undefined) {showClose.value = options.showClose;}
    if (options.modal !== undefined) {modal.value = options.modal;}
    if (options.closeOnClickModal !== undefined) {closeOnClickModal.value = options.closeOnClickModal;}
    if (options.beforeCloseCallback) {beforeCloseCallback.value = options.beforeCloseCallback;}
    if (options.appendToBody !== undefined) {appendToBody.value = options.appendToBody;}
    if (options.destroyOnClose !== undefined) {destroyOnClose.value = options.destroyOnClose;}
    if (options.fullscreen !== undefined) {fullscreen.value = options.fullscreen;}
    
    visible.value = true;
    return {
      close,
      showLoading,
      hideLoading,
      updateContent: (newContent) => content.value = newContent
    };
  };
  
  // 关闭弹窗的方法
  const close = () => {
    visible.value = false;
    hideLoading();
  };
  
  // 显示loading
  const showLoading = (options = {}) => {
    if (!loading.value) {
      loading.value = true;
      loadingInstance = ElLoading.service({
        target: ".el-dialog__body",
        ...options
      });
    }
  };
  
  // 隐藏loading
  const hideLoading = () => {
    if (loading.value && loadingInstance) {
      loading.value = false;
      loadingInstance.close();
      loadingInstance = null;
    }
  };
  
  // 计算属性：弹窗最终配置
  const dialogProps = computed(() => ({
    modelValue: visible.value,
    title: title.value,
    width: width.value,
    showClose: showClose.value,
    modal: modal.value,
    closeOnClickModal: closeOnClickModal.value,
    beforeClose: beforeCloseCallback.value,
    appendToBody: appendToBody.value,
    destroyOnClose: destroyOnClose.value,
    fullscreen: fullscreen.value,
    "onUpdate:modelValue": (val) => visible.value = val
  }));
  
  return {
    open,
    close,
    showLoading,
    hideLoading,
    dialogProps,
    visible,
    content,
    headerContent,
    footerContent
  };
}