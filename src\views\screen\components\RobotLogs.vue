<template>
  <div class="screen-card log-card">
    <div class="card-header">
      <span class="card-title">机器人服务器运行日志</span>
    </div>
    <el-table
      ref="serverTable"
      :data="logsList"
      height="250"
      style="width: 100%"
      :header-cell-style="{background:'#1f2d3d', color:'#fff'}"
      :row-style="{background:'#304156', color:'#bfcbd9'}"
      stripe
      @mouseenter="cellMouseEnter"
      @mouseleave="cellMouseLeave"
    >
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column prop="logType" show-overflow-tooltip label="类型" width="60" />
      <el-table-column prop="logInfo" show-overflow-tooltip label="消息内容" />
      <el-table-column prop="createTime" show-overflow-tooltip label="更新时间" width="150">
        <template #default="{row}">
          {{ dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import dayjs from "dayjs";
import useTableScroll from "@/composables/useTableScroll";
import { getServerLog } from "@/api/log";

const serverTable = ref(null);
const { autoScroll, clearScroll, cellMouseEnter, cellMouseLeave } = useTableScroll(serverTable);

const logsList = ref([]);

const getServerList = async () => {
  const res = await getServerLog({ skipCount: 0, maxResultCount: 20 });
  logsList.value = res.data?.items;
  autoScroll();
};

onMounted(() => {
  getServerList();
});
onUnmounted(() => {
  clearScroll();
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>