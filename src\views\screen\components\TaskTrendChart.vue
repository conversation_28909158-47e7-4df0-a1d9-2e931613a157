<template>
  <div class="screen-card task-trend-card">
    <div class="card-header">
      <span class="card-title">任务趋势统计</span>
    </div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup>
//import dayjs from "dayjs";
import { onMounted } from "vue";
import { useEcharts } from "@/composables/useEcharts";
import { getTaskChartOptions } from "../config/chartOptions";
import { getTaskNum } from "@/api/screen";

const { chartRef, setOptions } = useEcharts();
const options = getTaskChartOptions();

// 获取任务数量
const getTaskNumData = async () => {
  const res = await getTaskNum({ periodType: 2 });
  const dataX = [], dataY = [];
  for (let i = 0; i < res.data.length; i++) {
    dataX.push((res.data[i].day).slice(0, 10));
    dataY.push(res.data[i].taskNum);
  }
  options.xAxis[0].data = dataX;
  options.series[0].data = dataY;
  setOptions(options);
};


onMounted(() => {
  getTaskNumData();
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>