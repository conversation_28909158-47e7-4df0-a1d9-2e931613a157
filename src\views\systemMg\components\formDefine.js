// import { getUserGroup } from "@/api/systemMg";

const formFiled = [
  {
    type: "input",
    label: "用户名称",
    prop: "userName",
    placeholder: "请填写用户名称",
    rules: [
      { required: true, message: "请填写用户名称", trigger: "blur" },
    ],
  },
  {
    type: "input",
    label: "手机号码",
    prop: "phoneNumber",
    placeholder: "请填写手机号码",
    rules: [
      { required: true, message: "请填写手机号码", trigger: "blur" },
      { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ],
  },
  {
    type: "input",
    label: "邮箱",
    prop: "email",
    placeholder: "请填写邮箱",
    rules: [
      { required: true, message: "请填写邮箱", trigger: "blur" },
      { pattern: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, message: "请输入正确的邮箱格式", trigger: "blur" }
    ],
  },
  {
    type: "virtual-select",
    label: "用户组群",
    prop: "roleNames",
    placeholder: "请输入关键词搜索",
    multiple: true,
    props: { label: "name", value: "name" },
    options: [],
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  },
  /* {
    type: "remote-select",
    label: "用户组群",
    prop: "roleNames",
    placeholder: "请输入关键词搜索",
    multiple: true,
    labelField: "name",
    valueField: "name",
    loading: false,
    remoteOptions: [],
    remoteMethod: async (query) => {
      if (query === "") {
        return [];
      }
      try {
        const res = await getUserGroup({
          keywords: query,
          isPaging: false
        });
        return res.data.items || [];
      } catch (error) {
        console.error("获取用户组失败:", error);
        return [];
      }
    }, 
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  },*/
  {
    type: "input",
    label: "密码",
    prop: "password",
    inputType: "password",
    placeholder: "请填写密码",
    rules: [
      { required: true, message: "请填写密码", trigger: "blur" },
      //{ min: 6, max: 20, message: "密码长度在6到20个字符之间", trigger: "blur" },
      //{ pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{6,20}$/, message: "密码必须包含大小写字母和数字", trigger: "blur" }
    ],
  },
  {
    type: "radio",
    label: "是否启用",
    prop: "isActive",
    radioList: [
      { label: "是", value: true},
      { label: "否", value: false},
    ],
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  },
];

const formOption = {
  justify: "center",
  labelWidth: "100px",
  showResetButton: false
};

const formData = {
  userName: "",
  phoneNumber: "",
  email: "",
  isActive: true,
  roleNames: "",
  password: ""
};

const formFiled_group = [
  {
    type: "input",
    label: "用户组名称",
    prop: "name",
    placeholder: "请填写用户组名称",
    rules: [
      { required: true, message: "请填写用户组名称", trigger: "blur" },
    ],
  },
  /*  {
    type: "radio",
    label: "是否启用",
    prop: "isActive",
    radioList: [
      { label: "是", value: true},
      { label: "否", value: false},
    ],
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  },
  {
    type: "input",
    label: "备注",
    prop: "notes",
    inputType: "textarea",
    placeholder: "请填写备注",
  } */
];

const formData_group = {
  name: "",
  /* isActive: "",
  notes: "", */
};

export default {
  formFiled,
  formOption,
  formData,
  formFiled_group,
  formData_group
};