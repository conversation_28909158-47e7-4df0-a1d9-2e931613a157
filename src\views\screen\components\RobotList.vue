<template>
  <div class="screen-card robot-list-card">
    <div class="card-header">
      <span class="card-title">机器人列表</span>
    </div>
    <el-table
      ref="robotListTable"
      :data="robotList"
      style="width: 100%"
      :header-cell-style="{background:'#1f2d3d', color:'#fff'}"
      :row-style="{background:'#304156', color:'#bfcbd9'}"
      stripe
      @mouseenter="cellMouseEnter"
      @mouseleave="cellMouseLeave"
    >
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column prop="robotId" show-overflow-tooltip label="机器人编号" min-width="100" />
      <el-table-column prop="robotName" show-overflow-tooltip label="机器人名称" min-width="100" />
      <el-table-column prop="robotState" label="状态">
        <template #default="{row}">
          <el-tag :type="getStatusType(row.robotState)" effect="dark">
            {{ row.robotState }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="utilization" show-overflow-tooltip label="稼动率" min-width="100">
        <template #default="{row}">
          {{ (row.utilization * 100).toFixed(1) }}%
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import useTableScroll from "@/composables/useTableScroll";
import { signalRService } from "@/utils/signalR";
import { getMonitoringDatas } from "@/api/robot";

const robotListTable = ref(null);
const { autoScroll, clearScroll, cellMouseEnter, cellMouseLeave } = useTableScroll(robotListTable);

const robotList = ref([]);
const statusType = {
  "ERROR":  "danger",
  "RUN": "success" ,
  "IDLE": "primary",
  "STOP": "warning",
  "UNKNOWN": "info",
};

const signalRInitialized = ref(false);

const getStatusType = (val) => {
  return statusType[val];
};

const getRobotList = async () => {
  const res = await getMonitoringDatas();
  robotList.value = res.data;
  autoScroll();
};

const signalRUrl =  "/signalr-hubs/messaging" || import.meta.env.VITE_WEBSOCKET_URL;

// SignalR 更新处理
const handleSignalRUpdate = (updatedItem) => {
  const index = robotList.value.findIndex(item => item.robotId === updatedItem.robotId);
  if (index !== -1) {
    const item = robotList.value[index];
    if (updatedItem.robotState) {
      item.robotState = updatedItem.robotState;
    }
    if (updatedItem.robotStep) {
      item.robotStep = updatedItem.robotStep;
    }
    if (updatedItem.utilization !== -1) {
      item.utilization = updatedItem.utilization;
    }
  }
};

// 初始化SignalR连接
const initSignalR = async () => {
  try {
    if (!signalRInitialized.value) {
      await signalRService.initConnection(signalRUrl);
      // signalRService.onReceiveUpdate(handleSignalRUpdate);
      signalRService.registerEventHandler("robotstatuschangedevent", handleSignalRUpdate);
      signalRInitialized.value = true;
    }
  } catch (error) {
    console.error("初始化SignalR失败:", error);
  }
};

// 清理SignalR连接
const cleanupSignalR = async () => {
  try {
    if (signalRInitialized.value) {
      signalRInitialized.value = false;
      if (signalRService.connection) {
        // signalRService.offReceiveUpdate(handleSignalRUpdate);
        signalRService.unregisterEventHandler("robotstatuschangedevent", handleSignalRUpdate);
      }
      await signalRService.stopConnection();
    }
  } catch (error) {
    console.error("清理SignalR连接失败:", error);
  }
};

onMounted(async() => {
  await getRobotList();
  await initSignalR();
});

onBeforeUnmount(async () => {
  await cleanupSignalR();
});

onUnmounted(() => {
  clearScroll();
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");

:deep(.el-tag--dark){
  &.el-tag--primary{
    background-color: #fadb14;
    border-color: #fadb14;
  }

  &.el-tag--success{
    background-color: #52c41a;
    border-color: #52c41a;
  }

  &.el-tag--danger{
    background-color: #f5222d;
    border-color: #f5222d;
  }

  &.el-tag--warning{
    background-color: #00fefe;
    border-color: #00fefe;
  }

  &.el-tag--info{
    background-color: #a5a5a5;
    border-color: #a5a5a5;
  }

}
</style>