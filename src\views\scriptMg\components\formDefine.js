// import { getScriptGroupList } from "@/api/script";

const formFiled = [
  {
    type: "input",
    label: "剧本编号",
    prop: "scriptId",
    placeholder: "请填写剧本编号",
    rules: [
      { required: true, message: "请填写剧本编号", trigger: "blur" },
    ],
    readonly: false,
  },
  {
    type: "input",
    label: "剧本名称",
    prop: "scriptName",
    placeholder: "请填写剧本名称",
    rules: [
      { required: true, message: "请填写剧本名称", trigger: "blur" },
    ],
  },
  {
    type: "input",
    label: "剧本版本号",
    prop: "scriptVersion",
    placeholder: "请填写剧本版本号",
    rules: [
      { required: true, message: "请填写剧本版本号", trigger: "blur" },
    ],
  },
  {
    type: "input",
    label: "剧本所有者",
    prop: "scriptOwner",
    placeholder: "请填写剧本所有者",
    rules: [
      { required: true, message: "请填写剧本所有者", trigger: "blur" },
    ],
  },
  /* {
    type: "radio",
    label: "是否启用",
    prop: "isActive",
    radioList: [
      { label: "是", value: true},
      { label: "否", value: false},
    ],
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  }, */
  {
    type: "virtual-select",
    label: "剧本组群",
    prop: "scriptGroupIds",
    placeholder: "请输入关键词搜索",
    multiple: true,
    props: { label: "scriptGroupName", value: "scriptGroupId" },
    options: [],
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  },
  /* {
    type: "remote-select",
    label: "剧本组群",
    prop: "scriptGroupIds",
    placeholder: "请输入关键词搜索",
    multiple: true,
    labelField: "scriptGroupName",
    valueField: "scriptGroupId", 
    loading: false,
    remoteOptions: [],
    remoteMethod: (query) => {
      return new Promise((resolve) => {
        if (query === "") {
          resolve([]);
          return;
        }
        getScriptGroupList({
          keywords: query,
          isPaging: false
        }).then((res) => {
          resolve(res.data.items);
        });
      });
    },
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  }, */
  {
    type: "input",
    label: "备注",
    prop: "notes",
    inputType: "textarea",
    placeholder: "请填写备注",
  }
];

const formOption = {
  justify: "center",
  labelWidth: "100px",
  showResetButton: false
};

const formData = {
  scriptId: "",
  scriptName: "",
  scriptVersion: "",
  isActive: true,
  scriptGroupIds: [],
  notes: "",
  scriptOwner: ""
};

const formFiled_group = [
  {
    type: "input",
    label: "剧本组编号",
    prop: "scriptGroupId",
    placeholder: "请填写剧本组编号",
    rules: [
      { required: true, message: "请填写剧本组编号", trigger: "blur" },
    ],
    readonly: false,
  },
  {
    type: "input",
    label: "剧本组名称",
    prop: "scriptGroupName",
    placeholder: "请填写剧本组名称",
    rules: [
      { required: true, message: "请填写剧本组名称", trigger: "blur" },
    ],
  },
  /* {
    type: "radio",
    label: "是否启用",
    prop: "isActive",
    radioList: [
      { label: "是", value: true},
      { label: "否", value: false},
    ],
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  }, */
  {
    type: "input",
    label: "备注",
    prop: "notes",
    inputType: "textarea",
    placeholder: "请填写备注",
  }
];

const formData_group = {
  scriptGroupName: "",
  scriptGroupId: "",
  isActive: true,
  notes: ""
};

export default {
  formFiled,
  formOption,
  formData,
  formFiled_group,
  formData_group
};