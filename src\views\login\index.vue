<template>
  <div class="login-container">
    <div class="login-box">
      <!-- <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left.png" alt="login">
      </div> -->
      <div class="login-form">
        <div class="login-logo">
          <img class="login-icon" src="@/assets/images/logo.png" alt="">
          <h2 class="logo-text">
            {{ defaultSettings.title }}
          </h2>
        </div>
        <LoginForm />
      </div>
    </div>
  </div>
</template>
  
<script setup>
import LoginForm from "./components/LoginForm.vue";
import { defaultSettings } from "@/settings";
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  // background: linear-gradient(to right, #43cea2, #185a9d);
  background: url("@/assets/images/bg.jpg") no-repeat center center;
  background-size: cover;
}

.login-box {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  // justify-content: center;
  justify-content: end;
  align-items: center;

  .login-left {
    width: 600px;
    margin-right: 10%;
    display: none;

    .login-left-img {
      width: 100%;
      height: 100%;
    }
  }

  .login-form {
    width: 420px;
    padding: 40px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: rgb(255 255 255/ 90%) 0 1px 10px 0px;
    margin-right: 8%;
    /* box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); */

    .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 45px;
        .login-icon {
          width: 90px;
          height: 50px;
        }
        .logo-text {
          padding: 0 0 0 25px;
          margin: 0;
          font-size: 32px;
          font-weight: bold;
          color: #286db4;
          white-space: nowrap;
        }
    }
  }
    
}
@media screen and (width <= 1250px) {
  .login-left {
    display: none;
  }
}

@media screen and (width <= 600px) {
  .login-form {
    width: 97% !important;
  }
}

</style>
  