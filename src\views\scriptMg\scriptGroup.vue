<template>
  <div class="app-container">
    <EasyForm
      style="text-align: center;"
      :filed-list="formFileds"
      :form-data="formDatas"
      :options="formOptions"
      @submit="searchFn"
      @reset="reset"
    />
    <div class="op-wrapper" style="margin-bottom: 20px;">
      <el-button type="primary" @click="addScript">
        增加
      </el-button>
    </div>
    <EasyTable
      :loading="loading" 
      :columns="tableColumn" 
      :table-data="listData" 
      :options="{ showPagination: true }" 
      :pagination="pagination" 
      @command="TableAction" 
      @size-change="handleSizeChange" 
      @current-change="handleCurrentChange"
    />
    <FormDialog
      ref="fromDialog"
      :dialog-type="scriptFormType"
      :dialog-data="scriptFormData"
      @refresh="search"
    />
    <BindUsergroup ref="bindUsergroup" @refresh="search" />
  </div>
</template>

<script setup>
import { useTable } from "@/composables/useTable.js";
import listDefine from "./listDefine";
import FormDialog from "./components/dialogGroup.vue";
import BindUsergroup from "./components/bindUsergroup.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteScriptGroup } from "@/api/script.js";

const formFileds = listDefine.formFiled;
const formOptions = listDefine.formOption;
const formDatas = ref(listDefine.formData);
const fromDialog = ref(null);
const scriptFormType = ref("add");
const scriptFormData = ref({});
const bindUsergroup = ref(null);

const tableColumn = listDefine.tableCol_group;
const listData = ref([]);

const commonFn = inject("$commonUtils"); // 注入公共方法

const { 
  searchParam, 
  pagination, 
  tableData, 
  loading, 
  search, 
  reset, 
  handleSizeChange, 
  handleCurrentChange 
} = useTable({
  api: "/api/app/script/script-group-list",
  isPageable: true,
  manual: true,
  paramMapping: {
    current: "skipCount",
    pageSize: "maxResultCount",
    dateRange: {
      start: "startTime",
      end: "endTime"
    }
  }
});

watch(tableData, (newValue) => {
  newValue.map((item) => {
    item.rolesName = item.roleInfoes?.map((role) => role.roleName).join(",");
  });
  listData.value = newValue;
}, { immediate: true });

const TableAction = (command, row) => {
  switch (command) {
  case "edit":
    scriptFormType.value = "edit";
    scriptFormData.value = row;
    nextTick(() => {
      fromDialog.value.openFormDialog();
    });
    break;
  case "bind": 
    bindUsergroup.value.openFormDialog(row.id, row.roleInfoes);
    break;
  case "delete":
    ElMessageBox.confirm("是否确认删除?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }).then(async () => {
      try {
        const res = await deleteScriptGroup(row.scriptGroupId);
        if (res) {
          ElMessage.success("删除成功");
          search();
        } else {
          ElMessage.error("删除失败");
        }
      } catch (error) {
        ElMessage.error("删除失败: " + (error.message || "未知错误"));
      }
    });
    break;
  default:
    break;
  }
};

const searchFn = () => {
  if(formDatas.value.dateRange?.length > 0) {
    formDatas.value.dateRange = commonFn.formatDateRange(formDatas.value.dateRange);
  }
  searchParam.value = { ...formDatas.value };
  search();
};

const addScript = () => {
  scriptFormType.value = "add";
  scriptFormData.value = {};
  nextTick(() => {
    fromDialog.value.openFormDialog();
  });
};
</script>

<style lang="scss" scoped>

</style>