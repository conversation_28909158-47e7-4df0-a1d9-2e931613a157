<template>
  <div class="border-line">
    <h4>实时信息</h4>
    <el-divider />
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="12">
        <el-form-item label="机器人状态:">
          <span
            class="form-value status-other"
            :class="robotStateClass"
          >{{ robotInfo.robotState }}</span>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12" :md="12">
        <el-form-item label="稼动率:">
          <span class="form-value">
            <el-progress
              :percentage="robotInfo.utilization * 100"
              :stroke-width="6"
              :show-text="false"
              :color="robotInfo.utilization > 0.8 ? '#67C23A' : robotInfo.utilization > 0.5 ? '#E6A23C' : '#F56C6C'"
            />
            {{ formatUtilization(robotInfo.utilization) }}
          </span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="24">
        <el-form-item label="当前执行步骤:">
          <span class="form-value" :class="{ 'robotStep': robotInfo.robotStep }">
            {{ robotInfo.robotStep || '-' }}
          </span>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
const props = defineProps({
  robotInfo: {
    type: Object,
    required: true
  }
});

const robotStateClass = computed(() => ({
  "status-running": props.robotInfo.robotState === "RUN",
  "status-idle": props.robotInfo.robotState === "IDLE",
  "status-error": props.robotInfo.robotState === "ERROR",
  "status-stopped": props.robotInfo.robotState === "STOP"
}));

const formatUtilization = (num) => {
  return (num * 100).toFixed(1) + "%";
};
</script>

<style lang="scss" scoped>
@import url(./index.scss);

@mixin spanStyle {
  display: inline-block;
  color: #fff;
  padding: 0px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
}

.form-value {
  color: #1f2937;
  font-size: 14px;
  height: 24px;
  line-height: 24px;

  &.status-running {
    @include spanStyle;
    background: #52c41a;
  }

  &.status-idle {
    @include spanStyle;
    background: #fadb14;
  }
  
  &.status-stopped {
    @include spanStyle;
    background: #00fefe;
  }
  
  &.status-error {
    @include spanStyle;
    background: #f5222d;
  }

  &.robotStep {
    background: #eff6ff;
    color: #2563eb;
    font-weight: 500;
    padding: 0px 10px;
    border-radius: 10px;
    width: 100%;
  }
}

.status-other {
  @include spanStyle;
  background: #a5a5a5;
}

.el-progress {
  width: 100px;
  margin-right: 5px;
}
</style>