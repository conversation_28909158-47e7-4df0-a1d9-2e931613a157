import Cookies from "js-cookie";
import { defaultSettings } from "@/settings";

const TokenKey = defaultSettings.TokenKey;

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token);
}

export function removeToken() {
  return Cookies.remove(Token<PERSON>ey);
}

export const getItem = name => {
  const data = window.localStorage.getItem(name);
  try {
    return JSON.parse(data);
  } catch (err) {
    return data;
  }
};

export const setItem = (name, value) => {
  if (typeof value === "object") {
    value = JSON.stringify(value);
  }

  window.localStorage.setItem(name, value);
};

export const removeItem = name => {
  window.localStorage.removeItem(name);
};
