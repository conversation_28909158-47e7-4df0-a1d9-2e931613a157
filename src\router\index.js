import { createRouter, createWebHashHistory } from "vue-router";
import staticRoutes from "./modules/static";
import screenRoutes from "./modules/screen";
import robotRoutes from "./modules/robot";
import scriptRoutes from "./modules/script";
import logRoutes from "./modules/log";
import historyRoutes from "./modules/history";
import systemRoutes from "./modules/system";

// 动态路由（后续可通过接口获取）
export const dynamicRoutes = [
  screenRoutes,
  robotRoutes,
  scriptRoutes,
  logRoutes,
  historyRoutes,
  systemRoutes
];

// 合并所有路由（包括静态和动态）供权限管理使用
export const constantRoutes = [
  ...staticRoutes.filter(route => route.path !== "/:pathMatch(.*)*"),
  ...dynamicRoutes,
  {
    path: "/:pathMatch(.*)*",
    name: "404",
    meta: { title: "404" },
    component: () => import("@/views/error/404.vue"),
    hidden: true
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes: staticRoutes, // 只注册静态路由
});

export default router;
