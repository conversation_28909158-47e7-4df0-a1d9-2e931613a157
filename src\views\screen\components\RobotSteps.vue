<template>
  <div class="screen-card steps-card">
    <div class="card-header">
      <span class="card-title">机器人实时运行步骤</span>
    </div>
    <el-table
      ref="stepsTable"
      :data="stepsList"
      height="250"
      style="width: 100%"
      :header-cell-style="{background:'#1f2d3d', color:'#fff'}"
      :row-style="{background:'#304156', color:'#bfcbd9'}"
      stripe
      @mouseenter="cellMouseEnter"
      @mouseleave="cellMouseLeave"
    >
      <el-table-column type="index" label="序号" width="50" />
      <el-table-column prop="robotId" show-overflow-tooltip label="机器人编号" min-width="90px" />
      <el-table-column prop="robotName" show-overflow-tooltip label="机器人名称" min-width="90px" />
      <el-table-column prop="robotStep" show-overflow-tooltip label="步骤名称" min-width="150" />
      <el-table-column prop="createTime" show-overflow-tooltip label="更新时间" min-width="150">
        <template #default="{row}">
          {{ dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import dayjs from "dayjs";
import useTableScroll from "@/composables/useTableScroll";
import { getStepLog } from "@/api/log";

const stepsTable = ref(null);
const { autoScroll, clearScroll, cellMouseEnter, cellMouseLeave } = useTableScroll(stepsTable);

const stepsList = ref([]);

const getStepsList = async () => {
  const res = await getStepLog({ skipCount: 0, maxResultCount: 20 });
  stepsList.value = res.data.items;
  autoScroll();
};

onMounted(() => {
  getStepsList();
});
onUnmounted(() => {
  clearScroll();
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>