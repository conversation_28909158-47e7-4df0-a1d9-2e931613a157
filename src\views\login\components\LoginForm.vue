<template>
  <el-form
    ref="loginFormRef"
    :model="loginForm"
    :rules="loginRules"
    size="large"
  >
    <el-form-item prop="name" label="账号:">
      <el-input
        v-model="loginForm.name"
        placeholder="请输入"
      >
        <template #prefix>
          <el-icon class="el-input__icon">
            <user />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="psw" label="密码:">
      <el-input
        v-model="loginForm.psw"
        type="password"
        placeholder="请输入"
        show-password
        autocomplete="new-password"
      >
        <template #prefix>
          <el-icon class="el-input__icon">
            <lock />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
  <div class="login-btn">
    <el-button :icon="CircleClose" round size="large" @click="resetForm(loginFormRef)">
      重置
    </el-button>
    <el-button
      :icon="UserFilled"
      round size="large" type="primary"
      :loading="loading" @click="loginFn(loginFormRef)"
    >
      登录
    </el-button>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useLoginStore } from "@/store/modules/login";
import { CircleClose, UserFilled } from "@element-plus/icons-vue";
// eslint-disable-next-line no-unused-vars
import { ElForm, ElMessage } from "element-plus";
import { login } from "@/api/login";

const router = useRouter();
const loginStore = useLoginStore();
const loginFormRef = ref(ElForm);

const loginRules = reactive({
  name: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  psw: [{ required: true, message: "请输入密码", trigger: "blur" }]
});

const loading = ref(false);
const loginForm = reactive({
  name: "",
  psw: ""
});

// resetForm
const resetForm = (formEl) => {
  if (!formEl) {
    return;
  }
  formEl.resetFields();
};
// login
const loginFn =(formEl) => {
  if (!formEl) {return;}
  formEl.validate(async valid => {
    if (!valid) {return;}
    loading.value = true;
    login({ ...loginForm }).then(async res => {
      loading.value = false;
      if (res.code === 200) {
        loginStore.login(res.data);
        ElMessage.success("登录成功");
        router.push({ path: "/dashboard" });
      } else {
        ElMessage.error(res.description);
        resetForm(loginFormRef.value);
      }
    }).catch(() => {
      loading.value = false;
    });
  });
};

onMounted(() => {
  // console.log(1111);
});
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 30px;
}

.login-btn {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 30px;
  white-space: nowrap;

  .el-button {
    width: 48%;
  }
}
</style>