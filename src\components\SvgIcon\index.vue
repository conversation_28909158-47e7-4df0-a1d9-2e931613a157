<template>
  <svg
    class="svg-icon"
    :style="{ width, height }"
  >
    <use
      :xlink:href="prefix + name"
    />
  </svg>
</template>
<script setup>
defineProps({
  // xlink:href的属性值前缀
  prefix: {
    type: String,
    default: "#icon-",
  },
  // 需要使用的svg的图标的名字
  name: {
    type: String,
    required: true,
  },
  // 需要使用的svg的图标的宽度
  width: {
    type: String,
    default: "16px",
  },
  // 需要使用的svg的图标的高度
  height: {
    type: String,
    default: "16px",
  },
});
</script>

<style scoped>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

</style>
