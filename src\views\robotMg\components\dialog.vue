<template>
  <div class="robot-dialog">
    <dialog-provider ref="formDialogRef">
      <EasyForm
        ref="easyForm"
        :filed-list="formFileds"
        :form-data="formDatas"
        :options="formOptions"
        style="text-align: center;margin: 10px 20px 0;"
        @submit="submitForm"
        @cancel="cancelForm"
        @success="successForm"
      />
    </dialog-provider>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import formDefine from "./formDefine.js";
import { addRobot, updateRobot } from "@/api/robot.js";

const props = defineProps({
  dialogType: {
    type: String,
    default: "add"
  },
  dialogData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["refresh"]);

const formDialogRef = ref(null);
const easyForm = ref(null);
const formFileds = ref([...formDefine.formFiled]);
const formOptions = formDefine.formOption;
const formDatas = ref({...formDefine.formData});
const isSubmitting = ref(false);

const titleName = computed(() => 
  props.dialogType === "add" ? "增加机器人" : "编辑机器人"
);

// 打开表单对话框
const openFormDialog = () => {
  // 重置表单数据
  formDatas.value = props.dialogType === "edit" 
    ? { ...props.dialogData } 
    : { ...formDefine.formData };

  // 设置编辑模式下的只读字段
  if (props.dialogType === "edit") {
    formFileds.value.forEach(field => {
      if (field.prop === "robotId") {
        field.readonly = true;
      }
    });
  } else {
    formFileds.value.forEach(field => {
      field.readonly = false;
    });
  }

  formDialogRef.value?.open({
    title: titleName.value,
    width: "500px",
    closeOnClickModal: false
  });
};
  
// 取消表单
const cancelForm = () => {
  formDialogRef.value.close();
};
  
// 提交表单
const submitForm = async (data) => {
  if (isSubmitting.value) {return;}
  formDialogRef.value.showLoading();
  try {
    isSubmitting.value = true;
    let res;
    if (props.dialogType === "add") {
      res = await addRobot(data);
    } else {
      res = await updateRobot(data);
    }
    if (res.code !== 200) {
      ElMessage.error(res.msg);
    } else {
      ElMessage.success(props.dialogType === "add" ? "添加成功" : "编辑成功");
    }
    formDialogRef.value.hideLoading();
    formDatas.value = {};
    formDialogRef.value.close();
  } catch (error) {
    console.error("表单提交失败:", error);
  } finally {
    formDialogRef.value.hideLoading();
    isSubmitting.value = false;
  }
};

// 表单提交成功回调
const successForm = () => {
  emit("refresh"); // 触发刷新事件
  // console.log("提交成功");
};


defineExpose({
  openFormDialog
});

</script>