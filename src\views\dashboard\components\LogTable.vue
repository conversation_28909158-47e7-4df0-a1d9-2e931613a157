<template>
  <div class="border-line">
    <div class="radio-group">
      <h4>{{ title }}</h4>
      <el-radio-group v-model="time" size="small" fill="#6cf" @change="handleTimeChange">
        <el-radio-button label="1h" value="1" />
        <el-radio-button label="3h" value="3" />
        <el-radio-button label="6h" value="6" />
      </el-radio-group>
    </div>
    <el-divider />
    <el-table :data="tableData" height="300px" style="width: 100%">
      <slot />
    </el-table>
    <div class="pagination">
      <el-pagination 
        layout="prev, pager, next" 
        :total="total"
        :current-page="currentPage" 
        :page-size="pageSize"
        @current-change="handleCurrentChange"
      />
      <el-button size="small" text type="primary" @click="handleNavigate">
        跳转>>
      </el-button>
    </div>
  </div>
</template>

<script setup name="LogTable">
import dayjs from "dayjs";

defineProps({
  // 表格标题
  title: {
    type: String,
    required: true,
    default: "日志列表"
  },
  // 表格数据
  tableData: {
    type: Array,
    required: true,
    default: () => []
  },
  // 总条数
  total: {
    type: Number,
    required: true
  },
  // 当前页码
  currentPage: {
    type: Number,
    required: true
  },
  // 每页条数
  pageSize: {
    type: Number,
    default: 10
  }
});

// 定义事件
const emit = defineEmits(["update:currentPage", "navigate", "timeChange"]);

// 页码改变处理
const handleCurrentChange = (val) => {
  emit("update:currentPage", val);
};

// 跳转处理
const handleNavigate = () => {
  emit("navigate");
};

const time = ref("1");

/**
 * 获取指定小时数的时间范围
 * @param {string} hours - 小时数，如"1"、"3"或"6"
 * @returns {Object} 包含开始时间和结束时间的对象
 */
const getTimeRange = (hours) => {
  const endTime = new Date(); 
  let startTime = new Date();
  startTime.setHours(endTime.getHours() - parseInt(hours));
  
  return {
    startTime: dayjs(startTime).format("YYYY-MM-DD HH:mm:ss"),
    endTime: dayjs(endTime).format("YYYY-MM-DD HH:mm:ss")
  };
};

const handleTimeChange = (val) => {
  time.value = val;
  const timeRange = getTimeRange(val);
  emit("timeChange", timeRange);
};

defineExpose({
  handleTimeChange
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");

@mixin flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 10px
}

.radio-group {
  @include flex;
}
.pagination {
  @include flex;
  padding-left: 10px;
}
</style>