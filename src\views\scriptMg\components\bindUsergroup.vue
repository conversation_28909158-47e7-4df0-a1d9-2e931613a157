<template>
  <div class="bind-userGroup">
    <dialog-provider ref="formDialogRef">
      <el-form>
        <el-form-item label="选择用户组">
          <el-select
            v-model="selectedGroup"
            placeholder="请选择"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteMethod"
            :loading="loading"
            @change="chosedUserGroup"
          >
            <el-option
              v-for="item in userGroupList"
              :key="item.id"
              :label="item.name"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="groupTags.length > 0" label="已选用户组">
          <el-tag
            v-for="tag in groupTags"
            :key="tag"
            closable
            :disable-transitions="false"
            @close="handleClose(tag)"
          >
            {{ tag }}
          </el-tag>
        </el-form-item>
        <el-form-item class="submit-btn">
          <el-button type="primary" @click="submitForm">
            提交
          </el-button>
          <el-button @click="cancelForm">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </dialog-provider>
  </div>
</template>

<script setup>
import { getUserGroup } from "@/api/systemMg";
import { bindUserGroup } from "@/api/script";
import { ElMessage } from "element-plus";

const formDialogRef = ref(null);
const isSubmitting = ref(false);
const userGroupList = ref([]);
const userGroupId = ref([]);
const groupTags = ref([]);
const loading = ref(false);
const selectedGroup = ref(null);
const scriptGroupId = ref("");

const emit = defineEmits(["refresh"]);

// 远程搜索方法
const remoteMethod = async (query) => {
  if (query) {
    loading.value = true;
    if (query === "") {
      userGroupList.value = [];
      loading.value = false;
      return;
    }
    try {
      const res = await getUserGroup({
        keywords: query,
        isPaging: false
      });
      userGroupList.value = res.data.items || [];
    } catch (error) {
      console.error("获取用户组失败:", error);
    } finally {
      loading.value = false;
    }
  } else {
    userGroupList.value = [];
  }
};

const openFormDialog = (id, data = []) => {
  scriptGroupId.value = id;
  groupTags.value = data.map(item => item.roleName) || [];
  userGroupId.value = data.map(item => item.roleId) || [];
  
  formDialogRef.value?.open({
    title: "绑定用户组",
    width: "500px",
    closeOnClickModal: false
  });
};

// 选择用户组
const chosedUserGroup = (val) => {
  if (!val) {return;}
  
  if (!userGroupId.value.includes(val.roleId)) {
    userGroupId.value.push(val.id);
    groupTags.value.push(val.name);
  }
  
  selectedGroup.value = null;
};

// 删除标签
const handleClose = (tag) => {
  const index = groupTags.value.indexOf(tag);
  if (index > -1) {
    groupTags.value.splice(index, 1);
    userGroupId.value.splice(index, 1);
  }
};

// 取消表单
const cancelForm = () => {
  groupTags.value = [];
  userGroupId.value = [];
  formDialogRef.value.close();
};

// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) {return;}
  try {
    isSubmitting.value = true;
    const res = await bindUserGroup({
      scriptGroupId: scriptGroupId.value,
      roleIds: userGroupId.value
    });
    if (res.code !== 200) {
      ElMessage.error(res.msg);
    } else {
      ElMessage.success("绑定成功");
      emit("refresh");
      formDialogRef.value.close();
    }
  } catch (error) {
    console.error("提交失败:", error);
  } finally {
    isSubmitting.value = false;
  }
};

defineExpose({
  openFormDialog
});
</script>

<style lang="scss" scoped>
.submit-btn {
  :deep(.el-form-item__content) {
    justify-content: center;
  }
}

.el-tag {
  margin-right: 8px;
  margin-top: 4px;
  margin-bottom: 4px;
}
</style>