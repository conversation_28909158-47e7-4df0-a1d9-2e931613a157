import { defineStore } from "pinia";
import { dynamicRoutes } from "@/router";
import router from "@/router";
import Layout from "@/layout/index.vue";
const modules = import.meta.glob("/src/views/**/**.vue");

/**
 * 将菜单数据转换为路由配置
 * @param {Array} menuData - 菜单数据数组
 * @returns {Array} - 路由配置数组
 */
function transformMenuToRoutes(menuData) {
  const generateRouteName = (url, suffix = "") => {
    return url.replace(/^\//, "").replace(/\//g, "") + suffix;
    
  };

  const getComponent = (url, isLink = false) => {
    if (isLink) {return null;}
    
    const indexPath = `/src/views${url}/index.vue`.replace(/\/+/g, "/");
    const directPath = `/src/views${url}.vue`;
    
    return modules[indexPath] || modules[directPath] || modules["/src/views/error/404.vue"];
  };

  const createMeta = (item) => ({
    icon: item.icon,
    title: item.name,
    isLink: item.isLink
  });

  const createRouteObject = (item, component, extraProps = {}) => ({
    path: item.url,
    name: generateRouteName(item.url, extraProps.nameSuffix || ""),
    meta: createMeta(item),
    component,
    ...extraProps
  });

  const menuMap = new Map();
  const rootMenus = [];
  
  menuData.forEach(item => {
    if (!item.parentId) {
      rootMenus.push(item);
    } else {
      if (!menuMap.has(item.parentId)) {
        menuMap.set(item.parentId, []);
      }
      menuMap.get(item.parentId).push(item);
    }
  });
  
  rootMenus.sort((a, b) => a.no - b.no);
  menuMap.forEach(children => {
    children.sort((a, b) => a.no - b.no);
  });

  const routes = [];
  
  rootMenus.forEach(rootMenu => {
    const children = menuMap.get(rootMenu.id) || [];
    
    if (rootMenu.isLink) {
      routes.push(createRouteObject(rootMenu, getComponent(rootMenu.url)));
      return;
    }

    if (children.length > 0) {
      const childRoutes = children.map(child => 
        createRouteObject(child, getComponent(child.url, child.isLink))
      );
      
      routes.push({
        ...createRouteObject(rootMenu, Layout),
        redirect: children[0].url,
        children: childRoutes
      });
    } else {
      const pathSegments = rootMenu.url.split("/").filter(Boolean);
      const parentPath = pathSegments.length > 1 
        ? `/${pathSegments.slice(0, -1).join("/")}` 
        : rootMenu.url;

      let parentRoute = routes.find(r => {
        const comparePath = parentPath === "/" 
          ? rootMenu.url.substring(0, rootMenu.url.lastIndexOf("/")) 
          : parentPath;
        return r.path === comparePath;
      });
      
      if (!parentRoute) {
        const parentUrl = rootMenu.url.substring(0, rootMenu.url.lastIndexOf("/")) || rootMenu.url;
        parentRoute = createRouteObject(
          { ...rootMenu, url: parentUrl },
          Layout,
          { redirect: rootMenu.url, children: [] }
        );
        routes.push(parentRoute);
      }
      
      parentRoute.children.push(
        createRouteObject(
          rootMenu, 
          getComponent(rootMenu.url, rootMenu.isLink),
          { nameSuffix: "Child" }
        )
      );
    }
  });
  
  return routes;
}

const NOT_FOUND_ROUTE = {
  path: "/:pathMatch(.*)*",
  name: "404",
  meta: { title: "404" },
  component: () => import("@/views/error/404.vue"),
  hidden: true
};

export const usePermissionStore = defineStore("permission", {
  state: () => ({
    routes: [],
    info: "",
    isRouteLoaded: false
  }),
  actions: {
    getInfo(userId) {
      this.info = userId;
    },
    async generateRoutes(data) {
      const isAdmin = localStorage.getItem("login-user") === "admin";
      const menuData = transformMenuToRoutes(data);

      const allMenus = [
        {
          path: "/",
          name: "Home",
          meta: { icon: "Monitor", title: "机器人监控", isAffix: true },
          redirect: "/dashboard",
          component: Layout,
          children: [
            {
              path: "/dashboard",
              name: "dashboard",
              component: () => import("@/views/dashboard/index.vue"),
            }
          ],
        },
        ...(isAdmin ? dynamicRoutes : menuData),
        NOT_FOUND_ROUTE
      ];
      
      this.routes = allMenus;

      this.isRouteLoaded = true;
      
      return allMenus;
    },

    removeRoute() {
      router.getRoutes().forEach(route => {
        if (route.name && route.name !== "login") {
          router.removeRoute(route.name);
        }
      });
      this.routes = [];
      this.isRouteLoaded = false;
    }
  }
});