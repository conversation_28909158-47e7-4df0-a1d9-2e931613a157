<template>
  <div class="easy-from">
    <el-form
      v-bind="_options"
      ref="formRef"
      :model="model"
      @submit.prevent
    >
      <el-row
        :gutter="20"
        :justify="_options.justify"
      >
        <el-col
          v-for="(item, index) in reactiveFiledList"
          v-show="!item.notShow"
          :key="index"
          :span="item.span || 24"
          :class="{'is-inline':_options.inline}"
        >
          <el-form-item
            v-bind="item"
            :label="item.label"
            :rules="item.rules"
          >
            <!-- 输入框 -->
            <el-input
              v-if="item.type == 'input'"
              v-model.trim="model[item.prop]" 
              :placeholder="item.placeholder || '请输入'"
              :type="item.inputType || 'text'"
              :disabled="item.disabled"
              :clearable="item.clearable || true"
              :readonly="item.readonly || false"
              :min="item.minLength || 0" 
              autocomplete="new-password"
              @keyup.enter="handleKeyup(item.enterable)"
            />
            <!-- 文本框 -->
            <el-input
              v-if="item.type == 'textarea'"
              v-model.trim="model[item.prop]" 
              :readonly="item.readonly" 
              :placeholder="item.placeholder || '请输入'"
              :type="item.type || 'text'"
              :disabled="item.disabled"
              :clearable="item.clearable || true"
            />
            <!-- 选择框 -->
            <el-select
              v-if="item.type == 'select' || item.type == 'remote-select'"
              v-model="model[item.prop]" 
              :placeholder="item.placeholder || '请选择'"
              :disabled="item.disabled || false"
              :multiple="item.multiple || false"
              :clearable="item.clearable || true"
              :filterable="item.type == 'remote-select'"
              :remote="item.type == 'remote-select'"
              :remote-method="item.type == 'remote-select' ? ((query) => handleRemoteSearch(query, item)) : undefined"
              :loading="item.loading"
              style="min-width: 150px;"
            >
              <el-option
                v-for="(option, sindex) in item.type == 'remote-select' ? item.remoteOptions : item.options"
                :key="sindex"
                :label="option[item.labelField || 'label']"
                :value="option[item.valueField || 'value']"
              />
            </el-select>
            <!-- 虚拟选择框 -->
            <el-select-v2
              v-if="item.type == 'virtual-select'"
              v-model="model[item.prop]" 
              :placeholder="item.placeholder || '请选择'"
              :disabled="item.disabled || false"
              :multiple="item.multiple || false"
              :clearable="item.clearable || true"
              filterable
              :options="item.options"
              :props="item.props"
              style="min-width: 150px;"
            />
            <!-- 复选框 -->
            <el-checkbox-group
              v-if="item.type == 'checkbox'"
              v-model="model[item.prop]"
              :placeholder="item.placeholder || '请选择'"
              :disabled="item.disabled"
              :clearable="item.clearable || true"
            >
              <el-checkbox
                v-for="(checkbox, cindex) in item.checkboxList"
                :key="cindex"
                :value="checkbox.value"
                :disabled="checkbox.disabled"
              >
                {{ checkbox.label }}
              </el-checkbox>
            </el-checkbox-group>
            <!-- 日期选择框 -->
            <el-date-picker
              v-if="item.type == 'date'"
              v-model="model[item.prop]" 
              :placeholder="item.placeholder || '请选择'"
              :disabled="item.disabled"
              :clearable="item.clearable || true"
              :type="item.dateType || 'date'"
              :format="item.format || 'YYYY-MM-DD'"
              :value-format="item.valueFormat || 'YYYY-MM-DD'"
              :style="{ 'width': item.width || '100%'}"
            />
            <!-- 日期时间选择框 -->
            <el-date-picker
              v-if="item.type == 'datetime'"
              v-model="model[item.prop]" 
              :placeholder="item.placeholder || '请选择'"
              :disabled="item.disabled"
              :clearable="item.clearable || true"
              :type="item.dateType || 'datetime'"
              :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
              :value-format="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
              :style="{ 'width': item.width || '100%'}"
            />
            <!-- 日期范围选择框 -->
            <el-date-picker
              v-if="item.type == 'daterange'"
              v-model="model[item.prop]" 
              :placeholder="item.placeholder || '请选择'"
              :disabled="item.disabled"
              :clearable="item.clearable || true"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :format="item.format || 'YYYY-MM-DD'"
              :value-format="item.valueFormat || 'YYYY-MM-DD'"
            />
            <!-- 单选框 -->
            <el-radio-group
              v-if="item.type == 'radio'"
              v-model="model[item.prop]"
              :placeholder="item.placeholder || '请选择'"
              :disabled="item.disabled"
              :clearable="item.clearable || true"
            >
              <el-radio
                v-for="(radio, rindex) in item.radioList"
                :key="rindex"
                :value="radio.value"
                :disabled="radio.disabled"
              >
                {{ radio.label }}
              </el-radio>
            </el-radio-group>
            <!-- 富文本编辑器 -->
            <wang-editor
              v-if="item.type == 'wangeditor'"
              :valuehtml="model[item.prop]"
              :self-disabled="item.disabled"
              @update:value-html="editorChange($event, item.prop)"
            />
          </el-form-item>
        </el-col>
        <!-- 按钮块 -->
        <el-form-item>
          <el-button
            type="primary"
            @click="onSubmit(formRef)"
          >
            {{ _options.submitButtonText }}
          </el-button>
          <el-button
            v-if="_options.showResetButton"
            type="info"
            plain
            @click="resetForm(formRef)"
          >
            {{ _options.resetButtonText }}
          </el-button>
          <el-button
            v-if="_options.showCancelButton"
            @click="cancelForm(formRef)"
          >
            {{ _options.cancelButtonText }}
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import WangEditor from "../WangEditor/index.vue";
import { useDebounceFn } from "@vueuse/core";

const props = defineProps({
  filedList: {
    type: Array,
    default: () => []
  },
  formData: {
    type: Object,
    default: () => {return {};}
  },
  options: {
    type: Object,
    default: () => { return {};}
  }
});
const formRef = ref(null);
const emit = defineEmits(["submit", "reset", "cancel", "success"]);

// 表单配置
const _options = computed(() => {
  const option = {
    justify: "start",
    labelPosition: "left",
    labelWidth: "80px",
    showResetButton: true,
    showCancelButton: true,
    inline: false,
    submitButtonText: "提交",
    resetButtonText: "重置",
    cancelButtonText: "取消"
  };
  return Object.assign(option, props?.options);
});

const model = computed({
  get() {
    return new Proxy(props.formData, {
      get(target, key, receiver) {
        return Reflect.get(target, key, receiver);
      },
      set(target, key, value, receiver) {
        return Reflect.set(target, key, value, receiver);
      }
    });
  }
});

const reactiveFiledList = ref([]);

watch(() => props.filedList, (newList) => {
  reactiveFiledList.value = newList.map(item => ({
    ...item,
    remoteOptions: item.remoteOptions || [],
    loading: item.loading || false
  }));
}, { immediate: true, deep: true });

const editorChange = (value, name) => {
  model.value[name] = value;
};

const doRemoteSearch = async (query, item) => {
  const reactiveItem = reactiveFiledList.value.find(field => field.prop === item.prop);
  
  if (query !== "") {
    reactiveItem.loading = true;
    if (typeof item.remoteMethod === "function") {
      try {
        const data = await item.remoteMethod(query);
        reactiveItem.remoteOptions = Array.isArray(data) ? [...data] : [];
      } catch (error) {
        console.error("远程搜索失败:", error);
        reactiveItem.remoteOptions = [];
      } finally {
        reactiveItem.loading = false;
      }
    }
  } else {
    reactiveItem.remoteOptions = [];
  }
};

const handleRemoteSearch = useDebounceFn((query, item) => {
  doRemoteSearch(query, item);
}, 500);

// 提交按钮
const onSubmit = async (formEl) => {
  if (!formEl) {
    return;
  }
  
  try {
    await formEl.validate();
    
    emit("submit", props.formData);
    
    await new Promise((resolve) => {
      const cleanup = watch(() => props.formData, () => {
        resetForm(formEl);
        emit("success");
        cleanup();
        resolve();
      });
    });
  } catch (error) {
    console.error("表单验证失败:", error);
    return false;
  }
};
// 输入框回车事件
const handleKeyup = (enterable) => {
  if (!enterable) {return;}
  onSubmit(formRef.value);
};
// 重置
const resetForm = (formEl) => {
  if (!formEl) {return;}
  formEl.resetFields();
  emit("reset", props.formData);
};

// 取消
const cancelForm = (formEl) => {
  if (!formEl) {return;}
  formEl.resetFields();
  emit("cancel");
};
</script>

<style lang="scss" scoped>
.is-inline {
  flex: none !important;
}
:deep(.el-form-item__label) {
  font-weight: 600;
}
</style>