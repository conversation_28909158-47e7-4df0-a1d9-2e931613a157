<template>
  <div class="app-container">
    <EasyForm
      style="text-align: center;"
      :filed-list="formFileds"
      :form-data="formDatas"
      :options="formOptions"
      @submit="searchFn"
      @reset="reset"
    />
    <div class="op-wrapper" style="margin-bottom: 20px;">
      <el-button type="primary" @click="addRobot">
        增加
      </el-button>
    </div>
    <EasyTable
      :loading="loading" 
      :columns="tableColumn" 
      :table-data="listData" 
      :options="{ showPagination: true }" 
      :pagination="pagination" 
      @command="TableAction" 
      @size-change="handleSizeChange" 
      @current-change="handleCurrentChange"
    />

    <FormDialog
      ref="fromDialog"
      :dialog-type="robotFormType"
      :dialog-data="robotFormData"
      @refresh="search"
    />
  </div>
</template>

<script setup>
import { useTable } from "@/composables/useTable.js";
import listDefine from "./listDefine";
import FormDialog from "./components/dialog.vue";
import { deleteRobot } from "@/api/robot.js";
import { ElMessage, ElMessageBox } from "element-plus";

const formFileds = listDefine.formFiled;
const formOptions = listDefine.formOption;
const formDatas = ref(listDefine.formData);
const fromDialog = ref(null);
const robotFormType = ref("add");
const robotFormData = ref({});

const tableColumn = listDefine.tableCol;
const listData = ref([]);

const commonFn = inject("$commonUtils"); // 注入公共方法

const { 
  searchParam, 
  pagination, 
  tableData, 
  loading, 
  search, 
  reset, 
  handleSizeChange, 
  handleCurrentChange 
} = useTable({
  api: "/api/app/robot/robot-list",
  isPageable: true,
  manual: true,
  paramMapping: {
    current: "skipCount",
    pageSize: "maxResultCount",
    dateRange: {
      start: "startTime",
      end: "endTime"
    }
  }
});

watch(tableData, (newValue) => {
  listData.value = newValue;
}, { immediate: true });

const TableAction = (command, row) => {
  switch (command) {
  case "edit":
    robotFormType.value = "edit";
    robotFormData.value = row;
    nextTick(() => {
      fromDialog.value.openFormDialog();
    });
    break;
  case "delete":
    ElMessageBox.confirm("是否确认删除?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }).then(async () => {
      try {
        const res = await deleteRobot(row.robotId);
        if (res) {
          ElMessage.success("删除成功");
          search();
        } else {
          ElMessage.error("删除失败");
        }
      } catch (error) {
        ElMessage.error("删除失败: " + (error.message || "未知错误"));
      }
    });
    break;
  default:
    break;
  }
};

const searchFn = () => {
  if(formDatas.value.dateRange?.length > 0) {
    formDatas.value.dateRange = commonFn.formatDateRange(formDatas.value.dateRange);
  }
  searchParam.value = { ...formDatas.value };
  search();
};

const addRobot = () => {
  robotFormType.value = "add";
  robotFormData.value = {};
  nextTick(() => {
    fromDialog.value.openFormDialog();
  });
};
</script>

<style lang="scss" scoped>

</style>