<template>
  <div class="app-container">
    <div class="table-header">
      <el-button type="primary" :loading="loading" @click="handleAddRoot">
        新增根菜单
      </el-button>
    </div>
    
    <MenuTable 
      :menu-list="menuList"
      :loading="loading"
      @add="handleAdd"
      @edit="handleEdit"
      @delete="handleDelete"
      @move="handleMove"
    />

    <!-- 菜单表单弹窗 -->
    <MenuForm
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :menu-data="menuForm"
      :loading="formLoading"
      @submit="submitForm"
      @cancel="dialogVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import MenuTable from "./components/MenuTable.vue";
import MenuForm from "./components/MenuForm.vue";
import { getMenu, deleteMenu, addMenu, editMenu } from "@/api/systemMg";
import listDefine from "./listDefine";

const menuList = ref([]);
const menuType = ref("add");
const dialogVisible = ref(false);
const dialogTitle = ref("");
const menuForm = ref({
  parentId: "",
  name: "",
  url: "",
  icon: "",
  isVisible: true
});
const loading = ref(false);
const formLoading = ref(false);



// 获取菜单列表
const getMenuList = async () => {
  loading.value = true;
  try {
    const res = await getMenu();
    menuList.value = listDefine.arrayToTree(res.data.items);
  } catch (error) {
    ElMessage.error("获取菜单列表失败");
  } finally {
    loading.value = false;
  }
};

// 新增根菜单
const handleAddRoot = () => {
  menuType.value = "add";
  menuForm.value = {
    parentId: "",
    name: "",
    url: "",
    no:"",
    icon: "",
    isVisible: true,
    isOutSide: false,
    isLink: ""
  };
  dialogTitle.value = "新增根菜单";
  dialogVisible.value = true;
};

// 新增子菜单
const handleAdd = (data) => {
  menuType.value = "add";
  menuForm.value = {
    parentId: data.id,
    name: "",
    url: "",
    no: "",
    icon: "",
    isVisible: true,
    isOutSide: false,
    isLink: ""
  };
  dialogTitle.value = "新增子菜单";
  dialogVisible.value = true;
};

// 编辑菜单
const handleEdit = (data) => {
  menuType.value = "edit";
  if(data.isLink) {data.isOutSide = true;}
  menuForm.value = { ...data };
  dialogTitle.value = "编辑菜单";
  dialogVisible.value = true;
};

// 递归收集所有子节点的name
const collectChildrenNames = (node) => {
  let names = [node.name];
  if (node.children && node.children.length > 0) {
    node.children.forEach(child => {
      names = names.concat(collectChildrenNames(child));
    });
  }
  return names;
};

// 删除菜单
const handleDelete = async (data) => {
  try {
    await ElMessageBox.confirm(
      `确认删除菜单"${data.name}"${data.children?.length ? "及其所有子菜单" : ""}吗？`, 
      "提示", 
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    loading.value = true;
    const menuNames = collectChildrenNames(data);
    await Promise.all(menuNames.map(name => deleteMenu({name})));
    ElMessage.success("删除成功");
    await getMenuList();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  } finally {
    loading.value = false;
  }
};

// 提交表单
const submitForm = async (formData) => {
  formLoading.value = true;
  try {
    const submitData = { ...formData };
    delete submitData.children;
    if (!submitData.isOutSide) {
      delete submitData.isLink;
    }
    delete submitData.isOutSide;

    if (menuType.value === "add") {
      delete submitData.id;
      await addMenu(submitData);
    } else {
      await editMenu(submitData);
    }
    
    ElMessage.success("保存成功");
    dialogVisible.value = false;
    await getMenuList();
  } catch (error) {
    ElMessage.error(error.message || "保存失败");
  } finally {
    formLoading.value = false;
  }
};

// 上移/下移菜单
const handleMove = async ({ row, direction }) => {
  loading.value = true;
  try {
    let siblings;
    if (!row.parentId || row.parentId === 0 || row.parentId === "0") {
      siblings = menuList.value;
    } else {
      const findParent = (list, pid) => {
        for (const item of list) {
          if (item.id === pid) {
            return item;
          }
          if (item.children) {
            const found = findParent(item.children, pid);
            if (found) {
              return found;
            }
          }
        }
        return null;
      };
      const parent = findParent(menuList.value, row.parentId);
      siblings = parent ? parent.children : [];
    }
    const idx = siblings.findIndex(item => item.id === row.id);
    if (idx === -1) {
      return;
    }

    let updatedNodes = [];
    if (direction === "up" && idx > 0) {
      const temp = siblings[idx].no;
      siblings[idx].no = siblings[idx - 1].no;
      siblings[idx - 1].no = temp;
      [siblings[idx - 1], siblings[idx]] = [siblings[idx], siblings[idx - 1]];
      updatedNodes = [siblings[idx - 1], siblings[idx]];
    } else if (direction === "down" && idx < siblings.length - 1) {
      const temp = siblings[idx].no;
      siblings[idx].no = siblings[idx + 1].no;
      siblings[idx + 1].no = temp;
      [siblings[idx], siblings[idx + 1]] = [siblings[idx + 1], siblings[idx]];
      updatedNodes = [siblings[idx], siblings[idx + 1]];
    } else {
      return;
    }

    siblings.forEach((item, i) => {
      item.no = String(i + 1);
    });

    await Promise.all(updatedNodes.map(node => {
      const updateData = { ...node };
      delete updateData.children;
      return editMenu(updateData);
    }));
    ElMessage.success("排序已更新");
  } catch (error) {
    ElMessage.error("排序更新失败");
    await getMenuList();
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getMenuList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .table-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
  }
}
</style>