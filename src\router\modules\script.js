import Layout from "@/layout/index.vue";

export default {
  path: "/scriptMg",
  name: "scriptMg",
  meta: { icon: "Memo", title: "剧本管理" },
  redirect: "/scriptMg/scriptSet",
  component: Layout,
  children: [
    {
      path: "/scriptMg/scriptSet",
      name: "scriptSet",
      meta: { icon: "Tickets", title: "剧本设置" },
      component: () => import("@/views/scriptMg/scriptSet.vue"),
    },
    {
      path: "/scriptMg/scriptGroup",
      name: "scriptGroup",
      meta: { icon: "svg-scriptgroup", title: "剧本组设置" },
      component: () => import("@/views/scriptMg/scriptGroup.vue"),
    }
  ],
};
