import Layout from "@/layout/index.vue";

export default {
  path: "/log",
  name: "log",
  meta: { icon: "svg-log", title: "日志管理" },
  redirect: "/log/robot",
  component: Layout,
  children: [
    {
      path: "/log/robot",
      name: "robot",
      meta: { icon: "svg-robot", title: "机器人状态" },
      component: () => import("@/views/log/robot.vue"),
    },
    {
      path: "/log/step",
      name: "step",
      meta: { icon: "svg-robot", title: "执行步骤" },
      component: () => import("@/views/log/step.vue"),
    },
    {
      path: "/log/server",
      name: "server",
      meta: { icon: "svg-server", title: "服务器" },
      component: () => import("@/views/log/server.vue"),
    },
  ],
};
