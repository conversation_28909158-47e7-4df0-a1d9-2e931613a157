import { createApp } from "vue";
//import "@/styles/index.scss"; // global css
// 引入路由文件
import router from "./router/index";
import App from "./App.vue";
// 引入ElementPlus
import "element-plus/dist/index.css";
// 引入element icon
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import "@/permission";
import pinia from "@/store/index.js";
// 注册全局组件
import globalComponent  from "./global-components";
// 导入SVG图标
import "virtual:svg-icons-register";
import CommonUtils from "@/utils/index";
// 导入自定义指令
import directives from "@/directives";


const app = createApp(App);
// 引入element icon
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
// 全局注册CommonUtils 组件内使用inject("$commonUtils");
const commonUtils = new CommonUtils();
app.provide("$commonUtils", commonUtils);

app.use(globalComponent);
app.use(pinia);
app.use(router);
app.use(directives);

app.mount("#app");