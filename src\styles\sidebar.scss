@use "./variables.scss" as variables;

#app {

    .main-container {
      min-height: 100%;
      transition: margin-left .28s;
      margin-left: variables.$sideBarWidth;
      position: relative;
    }
  
    .sidebar-container {
      transition: width 0.28s;
      width: variables.$sideBarWidth !important;
      background-color: variables.$menuBg;
      height: 100%;
      position: fixed;
      font-size: 0px;
      top: 0;
      bottom: 0;
      left: 0;
      z-index: 1001;
      overflow: hidden;
  
      // reset element-ui css
      .horizontal-collapse-transition {
        transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
      }
  
      .scrollbar-wrapper {
        overflow-x: hidden !important;
      }
  
      .el-scrollbar__bar.is-vertical {
        right: 0px;
      }
  
      .el-scrollbar {
        height: 100%;
      }
  
      &.has-logo {
        .el-scrollbar {
          height: calc(100% - 50px);
        }
      }
  
      .is-horizontal {
        display: none;
      }
  
      a {
        display: inline-block;
        width: 100%;
        overflow: hidden;
      }
  
      .svg-icon {
        margin-right: 8px;
        margin-left: 5px;
      }
  
      .sub-el-icon {
        margin-right: 12px;
        margin-left: -2px;
      }
  
      .el-menu {
        border: none;
        height: 100%;
        width: 100% !important;

        .el-menu-item {
          padding: 0 15px;

          .el-icon {
            margin-top: -3px;
          }

          .el-menu-tooltip__trigger {
            justify-content: center;

            .svg-icon {
              margin-right: 0px;
              margin-left: -5px;
            }
          }
        }

        .is-active {
          color: variables.$menuActiveText;
        }
      }
  
      // menu hover
      .sub-menu-title-noDropdown,
      .el-sub-menu__title {
        padding: 0 15px;

        &:hover {
          background-color: variables.$menuHover !important;
        }
      }
  
      .is-active>.el-sub-menu__title {
        color: variables.$subMenuActiveText !important;
      }
  
      & .nest-menu .el-sub-menu>.el-sub-menu__title,
      & .el-sub-menu .el-menu-item {
        min-width: variables.$sideBarWidth !important;
        background-color: variables.$subMenuBg !important;
        padding-left: 30px;
  
        &:hover {
          background-color: variables.$subMenuHover !important;
        }
      }
    }
  
    .hideSidebar {
      .sidebar-container {
        width: 54px !important;
      }
  
      .main-container {
        margin-left: 54px;
      }
  
      .sub-menu-title-noDropdown {
        padding: 0 !important;
        position: relative;
  
        .el-tooltip {
          padding: 0 !important;
  
          .svg-icon {
            margin-right: 0px;
            margin-left: 2px;
          }
  
          .sub-el-icon {
            margin-left: 19px;
          }
        }
      }
  
      .el-sub-menu {
        overflow: hidden;
  
        &>.el-sub-menu__title {
          // padding: 0 !important;
  
          .svg-icon {
            margin-right: 0px;
            margin-left: 2px;
          }
  
          .sub-el-icon {
            margin-left: 19px;
          }
  
          .el-sub-menu__icon-arrow {
            display: none;
          }
        }
      }
  
      .el-menu--collapse {
        .el-sub-menu {
          &>.el-sub-menu__title {
            &>span {
              height: 0;
              width: 0;
              overflow: hidden;
              visibility: hidden;
              display: inline-block;
            }
          }
        }

        .el-menu-item {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  
    .el-menu--collapse .el-menu .el-sub-menu {
      min-width: variables.$sideBarWidth !important;
    }
  
    // mobile responsive
    .mobile {
      .main-container {
        margin-left: 0px;
      }
  
      .sidebar-container {
        transition: transform .28s;
        width: variables.$sideBarWidth !important;
      }
  
      &.hideSidebar {
        .sidebar-container {
          pointer-events: none;
          transition-duration: 0.3s;
          transform: translate3d(-(variables.$sideBarWidth), 0, 0);
        }
      }
    }
  
    .withoutAnimation {
  
      .main-container,
      .sidebar-container {
        transition: none;
      }
    }
  }
  
  // when menu collapsed
  .el-menu--vertical {
    &>.el-menu {
      .svg-icon {
        margin-right: 0px;
        margin-left: 2px;
      }
      .sub-el-icon {
        margin-right: 12px;
        margin-left: -2px;
      }
    }
  
    .nest-menu .el-sub-menu>.el-sub-menu__title,
    .el-menu-item {
      &:hover {
        // you can use $subMenuHover
        background-color: variables.$menuHover !important;
      }
    }
  
    // the scroll bar appears when the subMenu is too long
    >.el-menu--popup {
      max-height: 100vh;
      overflow-y: auto;
  
      &::-webkit-scrollbar-track-piece {
        background: #d3dce6;
      }
  
      &::-webkit-scrollbar {
        width: 6px;
      }
  
      &::-webkit-scrollbar-thumb {
        background: #99a9bf;
        border-radius: 20px;
      }
    }
  }
  