<template>
  <div class="more-botton">
    <el-dropdown
      trigger="click"
      :teleported="false"
    >
      <div class="more-button">
        <el-icon><ArrowDown /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="refresh">
            <el-icon><Refresh /></el-icon>刷新
          </el-dropdown-item>
          <el-dropdown-item
            divided
            @click="closeCurrentTab"
          >
            <el-icon><Remove /></el-icon>关闭当前
          </el-dropdown-item>
          <el-dropdown-item @click="tabStore.closeTabsOnSide(route.fullPath, 'left')">
            <el-icon><DArrowLeft /></el-icon>关闭左侧
          </el-dropdown-item>
          <el-dropdown-item @click="tabStore.closeTabsOnSide(route.fullPath, 'right')">
            <el-icon><DArrowRight /></el-icon>关闭右侧
          </el-dropdown-item>
          <el-dropdown-item
            divided
            @click="tabStore.closeMultipleTab(route.fullPath)"
          >
            <el-icon><CircleClose /></el-icon>关闭其它
          </el-dropdown-item>
          <el-dropdown-item @click="closeAllTab">
            <el-icon><FolderDelete /></el-icon>关闭全部
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { inject, nextTick } from "vue";
import { useTabsStore } from "@/store/modules/tabs";
import { useRoute, useRouter } from "vue-router";

const route = useRoute();
const router = useRouter();
const tabStore = useTabsStore();

// refresh current page
const refreshCurrentPage = inject("refresh");
const refresh = () => {
  setTimeout(() => {
    route.meta.isKeepAlive && tabStore.removeKeepAliveName(route.fullPath);
    refreshCurrentPage(false);
    nextTick(() => {
      route.meta.isKeepAlive && tabStore.addKeepAliveName(route.fullPath);
      refreshCurrentPage(true);
    });
  }, 0);
};


// Close Current
const closeCurrentTab = () => {
  if (route.meta.isAffix) {return;}
  tabStore.removeTabs(route.fullPath);
};

// Close All
const closeAllTab = () => {
  tabStore.closeMultipleTab();
  router.push("/");
};
</script>

<style lang="scss" scoped>
@use "./index.scss";
</style>