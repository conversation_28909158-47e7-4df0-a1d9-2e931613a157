import { ref, computed, nextTick, onUnmounted } from "vue";

export function useVirtualScroll({ className, itemHeight, gap = 20, buffer = 2 }) {
  const containerRef = ref(null);
  const scrollTop = ref(0);
  const dataList = ref([]);
  const actualItemWidth = ref(0);
  
  // 存储清理函数
  let resizeObserverCleanup = null;

  // 计算实际卡片宽度
  const calculateActualWidth = () => {
    if (!containerRef.value) {return 280;} // 默认宽度
    
    const cardElements = containerRef.value.getElementsByClassName(className);
    if (cardElements && cardElements.length > 0) {
      // 获取第一个卡片的计算宽度
      const computedStyle = window.getComputedStyle(cardElements[0]);
      const width = parseFloat(computedStyle.width);
      if (!isNaN(width) && width > 0) {
        actualItemWidth.value = width;
        return actualItemWidth.value;
      }
    }
  
    const containerWidthValue = containerRef.value.clientWidth;
    const colCount = Math.floor((containerWidthValue + gap) / (280 + gap));
    const calculatedWidth = (containerWidthValue - (gap * (colCount - 1))) / colCount;
    actualItemWidth.value = Math.max(calculatedWidth, 100); // 最小宽度保护
    
    return actualItemWidth.value;
  };

  // 计算列数
  const columnsCount = computed(() => {
    if (!containerRef.value) {return 1;}
    const width = containerRef.value.clientWidth;
    const itemWidth = actualItemWidth.value || 280;
    return Math.max(1, Math.floor((width + gap) / (itemWidth + gap)));
  });

  // 计算行数
  const rowCount = computed(() => {
    return Math.ceil(dataList.value.length / columnsCount.value);
  });

  // 计算行高
  const rowHeight = computed(() => itemHeight + gap);

  // 计算总高度
  const totalHeight = computed(() => rowCount.value * rowHeight.value);

  // 计算开始行（优化边界处理）
  const startRow = computed(() => {
    return Math.max(0, Math.floor(scrollTop.value / rowHeight.value) - buffer);
  });

  // 计算偏移量
  const startOffset = computed(() => startRow.value * rowHeight.value);

  // 计算可见行数
  const visibleRowCount = computed(() => {
    if (!containerRef.value) {return 0;}
    return Math.ceil(containerRef.value.clientHeight / rowHeight.value) + buffer * 2;
  });

  // 计算可见数据
  const visibleData = computed(() => {
    const startIndex = startRow.value * columnsCount.value;
    const endIndex = Math.min(
      dataList.value.length,
      (startRow.value + visibleRowCount.value) * columnsCount.value
    );
    return dataList.value.slice(Math.max(0, startIndex), endIndex);
  });

  // 防抖处理滚动事件
  let scrollTimer = null;
  const handleScroll = () => {
    if (containerRef.value) {
      scrollTop.value = containerRef.value.scrollTop;
      
      if (scrollTimer) {
        clearTimeout(scrollTimer);
      }
      
      scrollTimer = setTimeout(() => {
        scrollTimer = null;
      }, 100);
    }
  };

  // 更新容器宽度和卡片宽度（优化性能）
  const updateContainerWidth = () => {
    if (!containerRef.value) {return;}
    
    // 保存之前的滚动位置和总高度
    const prevScrollTop = scrollTop.value;
    const prevTotalHeight = totalHeight.value;
    const isAtBottom = containerRef.value.scrollTop + containerRef.value.clientHeight >= prevTotalHeight - 20;
    
    // 等待DOM更新后，测量卡片宽度
    nextTick(() => {
      if (!containerRef.value) {return;}
      
      calculateActualWidth();
      
      if (isAtBottom && totalHeight.value !== prevTotalHeight) {
        containerRef.value.scrollTop = Math.max(0, totalHeight.value - containerRef.value.clientHeight);
      } else if (prevTotalHeight > 0) {
        const scrollRatio = prevScrollTop / prevTotalHeight;
        containerRef.value.scrollTop = Math.floor(scrollRatio * totalHeight.value);
      }
      
      handleScroll();
    });
  };

  // 防抖处理 resize 事件
  let resizeTimer = null;
  const debouncedUpdateContainerWidth = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
    resizeTimer = setTimeout(() => {
      updateContainerWidth();
      resizeTimer = null;
    }, 150);
  };

  // 使用ResizeObserver监听容器大小变化
  const setupResizeObserver = () => {
    if (!containerRef.value || typeof ResizeObserver === "undefined") {return;}
  
    const resizeObserver = new ResizeObserver(debouncedUpdateContainerWidth);
    resizeObserver.observe(containerRef.value);
  
    resizeObserverCleanup = () => {
      resizeObserver.disconnect();
    };
    
    return resizeObserverCleanup;
  };

  // 监听窗口大小变化
  window.addEventListener("resize", debouncedUpdateContainerWidth);

  // 清理函数
  const cleanup = () => {
    // 清理定时器
    if (scrollTimer) {
      clearTimeout(scrollTimer);
      scrollTimer = null;
    }
    if (resizeTimer) {
      clearTimeout(resizeTimer);
      resizeTimer = null;
    }
    
    // 清理事件监听器
    window.removeEventListener("resize", debouncedUpdateContainerWidth);
    
    // 清理 ResizeObserver
    if (resizeObserverCleanup) {
      resizeObserverCleanup();
      resizeObserverCleanup = null;
    }
    
    // 清理数据引用
    dataList.value = [];
    scrollTop.value = 0;
    actualItemWidth.value = 0;
  };

  // 自动清理（当组件卸载时）
  onUnmounted(cleanup);

  // 初始化函数
  const init = () => {
    if (containerRef.value) {
      calculateActualWidth();
      setupResizeObserver();
    }
  };

  return {
    containerRef,
    scrollTop,
    dataList,
    actualItemWidth,
    columnsCount,
    totalHeight,
    startOffset,
    visibleData,
    handleScroll,
    calculateActualWidth,
    setupResizeObserver,
    updateContainerWidth,
    cleanup,
    init // 新增初始化函数
  };
}