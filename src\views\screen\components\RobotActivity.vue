<template>
  <div class="screen-card activity-card">
    <div class="card-header">
      <span class="card-title">机器人活跃情况统计</span>
    </div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup>
import { useEcharts } from "@/composables/useEcharts";
import { getActivityOptions } from "../config/chartOptions";
import { getActivity } from "@/api/screen";

const { chartRef, setOptions } = useEcharts();
const options = getActivityOptions();

// 获取任务数量
const getActivityData = async () => {
  const res = await getActivity({ periodType: 2 });
  const dataX = [], activeRobots = [], inactiveRobots = [];
  
  for (let i = 0; i < res.data.length; i++) {
    const item = res.data[i];
    dataX.push(item.time.slice(0, 10));
    activeRobots.push(item.robotNum);
    inactiveRobots.push(item.robotTotalNum - item.robotNum);
  }
  
  options.xAxis.data = dataX;
  options.series[0].data = activeRobots;
  options.series[1].data = inactiveRobots;
  setOptions(options);
};

onMounted(() => {
  getActivityData();
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>