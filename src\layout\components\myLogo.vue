<template>
  <div
    class="sidebar-logo-container"
    :class="{'isCollapse':collapse}"
  >
    <transition name="sidebarLogoFade">
      <router-link
        v-if="!collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <img
          v-if="true"
          src="@/assets/images/logo.png"
          class="sidebar-logo"
        >
        <h1
          v-else
          class="text-xl sidebar-title "
        >
          {{ title }}
        </h1>
      </router-link>
      <router-link
        v-else
        key="expand"
        class="sidebar-logo-link"
        to="/"
      >
        <img
          src="@/assets/images/logo.png"
          class="sidebar-logo"
        >
        <h1 class="text-xl sidebar-title">
          {{ title }}
        </h1>
      </router-link>
    </transition>
  </div>
</template>

<script setup name="SiderbarLogo">
import { ref } from "vue";
import { defaultSettings } from "@/settings";
defineProps({
  collapse: {
    type: Boolean,
    required: true
  }
});

const title = ref(defaultSettings.title);
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
    opacity: 0;
}

.sidebar-logo-container {
    position: relative;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #2b2f3a;
    text-align: center;
    overflow: hidden;

    & .sidebar-logo-link {
        height: 100%;
        width: 100%;

        & .sidebar-logo {
            width: 32px;
            height: 20px;
            vertical-align: middle;
            display: inline-block;
        }

        & .sidebar-title {
            display: inline-block;
            margin: 0;
            color: #fff;
            font-weight: 600;
            font-size: 18px;
            line-height: 50px;
            font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
            vertical-align: middle;
            margin-left: 10px;
        }
    }

    &.isCollapse {
        .sidebar-logo {
            margin-right: 0px;
        }
    }
}
</style>
