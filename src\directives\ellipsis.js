/**
 * v-ellipsis 自定义指令
 * 检测元素内容是否溢出，如果溢出则添加title属性
 */
export default {
  mounted(el) {
    checkEllipsis(el);
    window.addEventListener("resize", () => checkEllipsis(el));
  },
  updated(el) {
    checkEllipsis(el);
  },
  unmounted(el) {
    window.removeEventListener("resize", () => checkEllipsis(el));
  }
};

/**
 * 检查元素是否溢出并设置title属性
 * @param {HTMLElement} el - 要检查的DOM元素
 */
function checkEllipsis(el) {
  const isEllipsis = el.offsetWidth < el.scrollWidth;
  
  if (isEllipsis) {
    el.setAttribute("title", el.textContent);
  } else {
    el.removeAttribute("title");
  }
}