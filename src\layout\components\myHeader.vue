<template>
  <div class="navbar">
    <hamburger
      :is-active="sidebar"
      class="hamburger-container"
      @toggle-click="toggleSideBar"
    />
    <breadcrumb class="breadcrumb-container" />
    <div class="right-menu">
      <!-- <template v-if="device!=='mobile'">
          <search id="header-search" class="right-menu-item" />  
          <error-log class="errLog-container right-menu-item hover-effect" />
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
          <el-tooltip content="Global Size" effect="dark" placement="bottom">
              <size-select id="size-select" class="right-menu-item hover-effect" />
          </el-tooltip>
      </template> -->
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img src="@/assets/images/admin.png" class="user-avatar" alt="">
          <span class="user-name">
            {{ loginStore.user || "admin" }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <span
                style="display:block;"
                @click="logoutFn"
              >退 出</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
<script setup>
import Hamburger from "@/components/Hamburger/index.vue";
import Breadcrumb from "@/components/Breadcrumb/index.vue";
import useAppStore from "@/store/app";
import { useLoginStore } from "@/store/modules/login";
import { usePermissionStore } from "@/store/modules/permission";
import { useTabsStore } from "@/store/modules/tabs";
import { ElMessageBox, ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { logout } from "@/api/login";


const router = useRouter();
const appStore = useAppStore(); // 获取app的sidebar状态
const loginStore = useLoginStore();
const sidebar = computed(() => appStore.sidebar);
const toggleSideBar = () => {
  appStore.changeSidebar();
};
function logoutFn() {
  ElMessageBox.confirm(
    "是否要退出登录?",
    "提示",
    {
      confirmButtonText: "退出",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      logout().then(() => {
        loginStore.logout();
  
        // 重置 Pinia 状态
        useTabsStore().resetState();
        // 重置权限状态
        usePermissionStore().removeRoute();

        ElMessage({
          type: "success",
          message: "登出成功",
        });
        // 跳转到登录页
        router.push("/login");
      });
    });
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  // padding: 0px 20px;
  line-height: 50px;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
    line-height: 50px;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        margin-top: 10px;
        position: relative;
        display: flex;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          border-radius: 10px;
        }

        .user-name {
          vertical-align: super;
        }

        .el-icon--right {
          cursor: pointer;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
  