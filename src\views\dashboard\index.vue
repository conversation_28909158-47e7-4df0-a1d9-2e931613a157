<template>
  <div v-loading="loading" class="app-container" style="position: relative;" element-loading-text="加载中">
    <div class="btn-wrapper">
      <el-popover placement="right" :width="280" trigger="click">
        <template #reference>
          <el-button size="small" :icon="Search" circle title="搜索" />
        </template>
        <el-input 
          v-model="searchName" 
          style="width: 250px" 
          placeholder="请输入机器人名称或设备名称"
          @input="handleSearch"
        />
      </el-popover>
      <el-button size="small" :icon="Refresh" circle title="刷新" @click="searchName = ''; fetchData" />
    </div>
    <div ref="containerRef" class="virtual-list-container" @scroll="handleScroll">
      <!-- 用于撑开滚动区域高度的元素 -->
      <div :style="{ height: totalHeight + 'px', position: 'relative' }" />
      
      <!-- 实际的网格容器 -->
      <div 
        v-if="dataList.length > 0"
        class="card-grid"
        :style="{ 
          transform: `translateY(${startOffset}px)`,
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%'
        }"
      >
        <el-card
          v-for="item in visibleData"
          :key="item.id"
          class="card-item shadow-lg"
        >
          <el-tooltip :content="item.isActive === true ? '已激活' : '未激活'" placement="top" effect="dark">
            <div 
              class="status-indicator"
              :class="{
                'active-running': item.isActive === true
              }"
            />
          </el-tooltip>
          
          <template #header>
            <div class="card-header" style="display: flex; align-items: end;">
              <img src="@/assets/images/robot.png" class="robot-icon">
              <span>{{ item.robotId }}</span>
            </div>
          </template>
          <p><span class="label">机器人名称</span> <span v-ellipsis class="content">{{ item.robotName }}</span></p>
          <p><span class="label">设备编号</span> <span v-ellipsis class="content">{{ item.eqpId }}</span></p>
          <p><span class="label">设备名称</span> <span v-ellipsis class="content">{{ item.eqpName }}</span></p>
          <p>
            <span class="label">机器人状态</span> <span
              v-ellipsis class="content robotState"
              :class="{
                'status-running': item.robotState === 'RUN',
                'status-idle': item.robotState === 'IDLE',
                'status-error': item.robotState === 'ERROR',
                'status-stopped': item.robotState === 'STOP'
              }"
            >{{ item.robotState }}</span>
          </p>
          <p>
            <span class="label">当前执行步骤</span> <span
              v-ellipsis class="content"
              :class="{
                'robotStep': item.robotStep,
              }"
            >{{ item.robotStep }}</span>
          </p>
          <p>
            <span class="label">稼动率</span>
            <span class="progress-container">
              <el-progress
                :percentage="item.utilization * 100"
                :stroke-width="6"
                :show-text="false"
                :color="item.utilization > 0.8 ? '#67C23A' : item.utilization > 0.5 ? '#E6A23C' : '#F56C6C'"
              />
              <span class="content">{{ formatUtilization(item.utilization) }}</span>
              <el-button size="small" text type="primary" @click="handleDetail(item)">
                >
              </el-button>
            </span>
          </p>
        </el-card>
      </div>
      <el-empty v-else :description="'暂无数据'" :image-size="200" />
    </div>
    <ViewDialog ref="viewDialog" :dialog-data="viewFormData" />
  </div>
</template>

<script setup>
import { Search, Refresh } from "@element-plus/icons-vue";
import ViewDialog from "./components/viewDialog.vue";
import { signalRService } from "@/utils/signalR";

import { useVirtualScroll } from "@/composables/useVirtualScroll";
import { getMonitoringDatas, getRobotInfo } from "@/api/robot";
import { useDebounceFn } from "@vueuse/core";

const CARD_HEIGHT = 240; // 卡片高度固定
const GRID_GAP = 20; // 网格间隙

const loading = ref(true);
const viewDialog = ref(null);
const viewFormData = ref({});

const signalRInitialized = ref(false); // 标记SignalR是否已初始化
const {
  containerRef,
  dataList,
  totalHeight,
  startOffset,
  visibleData,
  handleScroll,
  calculateActualWidth,
  updateContainerWidth,
  setupResizeObserver
} = useVirtualScroll({
  calssName: "card-item",
  itemHeight: CARD_HEIGHT,
  gap: GRID_GAP,
  buffer: 2
});

// 原始数据列表
const originalDataList = ref([]);
const searchName = ref("");

// 稼动率显示
const formatUtilization = (num) => {
  return (num * 100).toFixed(1) + "%";
};

const handleDetail = async(item) => {
  try {
    const response = await getRobotInfo(item.robotId);
    viewFormData.value = { ...response.data, ...item};
    nextTick(() => {
      viewDialog.value.openFormDialog();
    });
  } catch (error) {
    console.error("获取机器人信息失败:", error);
  }
};

// 使用防抖优化搜索
const debouncedSearch = useDebounceFn(() => {
  if (!searchName.value) {
    dataList.value = originalDataList.value;
    return;
  }
  const keyword = searchName.value.toLowerCase();
  dataList.value = originalDataList.value.filter(item => 
    item.robotName?.toLowerCase().includes(keyword) || 
    item.eqpName?.toLowerCase().includes(keyword)
  );
}, 300);

// 优化搜索处理
const handleSearch = () => {
  debouncedSearch();
};

// SignalR 更新处理
const handleSignalRUpdate = (updatedItem) => {
  const index = originalDataList.value.findIndex(item => item.robotId === updatedItem.robotId);
  if (index !== -1) {
    const item = originalDataList.value[index];
    if (updatedItem.robotState) {
      item.robotState = updatedItem.robotState;
    }
    if (updatedItem.robotStep) {
      item.robotStep = updatedItem.robotStep;
    }
    if (updatedItem.utilization !== -1) {
      item.utilization = updatedItem.utilization;
    }
  }
  if (updatedItem.robotId === viewFormData.value.robotId) {
    if (updatedItem.robotState) {
      viewFormData.value.robotState = updatedItem.robotState;
    }
    if (updatedItem.robotStep) {
      viewFormData.value.robotStep = updatedItem.robotStep;
    }
    if (updatedItem.utilization !== -1) {
      viewFormData.value.utilization = updatedItem.utilization;
    }
  }

  // 仅在搜索时才触发过滤
  if (searchName.value) {
    handleSearch();
  }
};

// 添加数据清理机制
const clearData = () => {
  originalDataList.value = [];
  dataList.value = [];
  viewFormData.value = {};
  searchName.value = "";
};

// 监听数据变化后计算卡片宽度
watch(dataList, () => {
  if (!loading.value) {
    nextTick(() => {
      calculateActualWidth();
    });
  }
}, { deep: true });

// 从API获取初始数据
const fetchData = async () => {
  try {
    loading.value = true;
    const response = await getMonitoringDatas();
    originalDataList.value = response.data;
    // 应用当前搜索条件
    handleSearch();
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
    // 数据加载完成后，计算卡片宽度
    nextTick(() => {
      calculateActualWidth();
    });
  }
};

// 监听搜索输入
watch(searchName, () => {
  handleSearch();
});

// 根据环境变量配置WebSocket连接地址
const signalRUrl =  "/signalr-hubs/messaging" || import.meta.env.VITE_WEBSOCKET_URL;

// 初始化SignalR连接
const initSignalR = async () => {
  try {
    if (!signalRInitialized.value) {
      await signalRService.initConnection(signalRUrl);
      // signalRService.onReceiveUpdate(handleSignalRUpdate);
      signalRService.registerEventHandler("robotstatuschangedevent", handleSignalRUpdate);
      signalRInitialized.value = true;
    }
  } catch (error) {
    console.error("初始化SignalR失败:", error);
  }
};

// 清理SignalR连接
const cleanupSignalR = async () => {
  try {
    if (signalRInitialized.value) {
      signalRInitialized.value = false;
      if (signalRService.connection) {
        // signalRService.offReceiveUpdate(handleSignalRUpdate);
        signalRService.unregisterEventHandler("robotstatuschangedevent", handleSignalRUpdate);
      }
      await signalRService.stopConnection();
    }
  } catch (error) {
    console.error("清理SignalR连接失败:", error);
  }
};

// 用于标记是否正在执行卸载，避免重复卸载
const isUnmounting = ref(false);

// 添加额外监听器，确保侧边栏折叠时更新布局
const cleanup = setupResizeObserver();
  
const mutationObserver = new MutationObserver(() => {
  setTimeout(updateContainerWidth, 300);
});

// 组件生命周期钩子
onBeforeUnmount(async () => {
  if (isUnmounting.value) {
    return;
  }
  
  isUnmounting.value = true;
  clearData();
  try {
    await cleanupSignalR();
  } catch (error) {
    console.error("组件卸载时清理资源失败:", error);
  }
});

onMounted(async () => {
  await fetchData();
  await initSignalR();
  
  if (containerRef.value) {
    containerRef.value.addEventListener("scroll", handleScroll);
  }
  
  updateContainerWidth();
  
  // 监听body或包含侧边栏的父元素的class变化
  mutationObserver.observe(document.body, {
    attributes: true,
    attributeFilter: ["class"],
    subtree: true
  });
});

onUnmounted(() => { 
  if (containerRef.value) {
    containerRef.value.removeEventListener("scroll", handleScroll);
  }
  
  window.removeEventListener("resize", updateContainerWidth);
  
  if (cleanup) {
    cleanup();
  }
  
  mutationObserver.disconnect();
});
</script>

<style lang="scss" scoped>
.btn-wrapper {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 20px;
  z-index: 999;

  :deep(.el-button+.el-button) {
    margin-left: 0;
    margin-top: 10px
  }
}
.virtual-list-container {
  height: calc(100vh - 170px);
  overflow-y: auto;
  position: relative;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 20px;
  box-sizing: border-box;
}

.card-item {
  position: relative;
  height: 240px;
  border-radius: 8px;
  background: #ffffff;
  overflow: hidden;
  transition: all .3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px #0000001a;
  }

  :deep(.el-card__header) {
    padding: 11px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    border-bottom: 1px #f3f4f6 solid;
  }

  .robot-icon {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }

  :deep(.el-card__body) {
    padding: 0px;
  }

  .card-info {
    flex: 1;
  }

  p {
    margin: 0;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;

    &:last-child {
      justify-content: space-between;
      border-bottom: none;
    }

    .label {
      color: #6b7280;
      font-size: 13px;
      width: 100px;
      flex-shrink: 0;
      padding: 6px 0px 6px 15px;
    }

    .content {
      color: #1f2937;
      margin: 0 8px;
      font-size: 13px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .robotStep {
      background: #eff6ff;
      color: #2563eb;
      font-weight: 500;
      padding: 2px 4px;
      border-radius: 5px;
    }

    .robotState {
      background: #a5a5a5;
      color: #fff;
      padding: 2px 16px;
      border-radius: 20px;
      font-size: 13px;
      font-weight: 500;

      &.status-running {
        background: #52c41a;
      }

      &.status-idle {
        background: #fadb14;
      }
      
      &.status-stopped {
        background: #00fefe;
      }
      
      &.status-error {
        background: #f5222d;
      }
    }

    .progress-container {
      display: flex;
      align-items: center;

      .el-progress {
        width: 60px;
      }
    }
  }

  .el-button {
    padding: 0 10px 0 0;
  }
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    box-shadow: var(0 0 #0000, 0 0 #0000), var(0 0 #0000, 0 0 #0000), var(--tw-shadow);
}

.status-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #a5a5a5; /* 默认颜色 */
  box-shadow: 0 0 4px rgba(165, 165, 165, 0.5);

  &.active-running {
    background-color: #52c41a;
    box-shadow: 0 0 8px rgba(82, 196, 26, 0.5);
  }

  /* .status-idle & {
    background-color: #fadb14;
    box-shadow: 0 0 8px rgba(250, 219, 20, 0.5);
  }
  
  .status-stopped & {
    background-color: #13c2c2;
    box-shadow: 0 0 8px rgba(19, 194, 194, 0.5);
  }
  
  .status-error & {
    background-color: #ff4d4f;
    box-shadow: 0 0 8px rgba(255, 77, 79, 0.5);
  } */
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    padding: 15px;
  }

  .el-progress {
    width: 35px !important;
  }
}
</style>