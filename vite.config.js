import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import eslint from "vite-plugin-eslint";
import path from "path";
import { createSvgIconsPlugin  } from "vite-plugin-svg-icons";
import AutoImport from "unplugin-auto-import/vite"; // 自动导入
import Components from "unplugin-vue-components/vite"; // 自动导入组件
import { ElementPlusResolver } from "unplugin-vue-components/resolvers"; // 按需引入element组件

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isProd = mode === "production";
  
  return {
    publicDir: "public",
    base: "./",
    server: {
      compress: true, // 启用 gzip 压缩
      port: 5555,
      open: true,
      host: "0.0.0.0",
      proxy: {
        "/api": {
          target: "http://*************:8800",
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/api/, "/api"),
          configure: (proxy) => {
            proxy.on("error", (err) => {
              console.log("proxy error", err);
            });
            proxy.on("proxyReq", (proxyReq, req) => {
              console.log("发送请求:", req.method, req.url);
            });
            proxy.on("proxyRes", (proxyRes, req) => {
              console.log("收到响应:", proxyRes.statusCode, req.url);
            });
          }
        },
        // 专门为 SignalR 添加的代理配置
        "/signalr-hubs": {
          target: "http://*************:8800",
          changeOrigin: true,
          ws: true, // 启用 WebSocket 代理
          configure: (proxy) => {
            proxy.on("error", (err) => {
              console.log("SignalR proxy error", err);
            });
            proxy.on("proxyReq", (proxyReq, req) => {
              console.log("SignalR 发送请求:", req.method, req.url);
            });
            proxy.on("proxyRes", (proxyRes, req) => {
              console.log("SignalR 收到响应:", proxyRes.statusCode, req.url);
            });
          }
        }
      },
    },
    optimizeDeps: {
      include: ["vue", "axios"],
      // force: true, // 强制进行依赖预构建
      splitChunks: {
        entries: ["app"], // 入口模块名称
        include: ["vue"], // 要包含的库, "lodash"
      },
      esbuildOptions: {
        minify: true,
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    css: {
      modules: {},
      minify: true, // 是否开启 CSS 压缩
      postcss: { // postCss 配置
        plugins: [],
      },
      preprocessorOptions: { // 全局样式配置
        scss: {
          use: ["vue-style-import"],
          api: "modern-compiler",
          silenceDeprecations: ["legacy-js-api"],
          additionalData: "@use \"@/styles/index.scss\" as *; @use \"@/styles/mixin.scss\" as *;" 
        },
      },
    },
    build: {
      target: "modules", //浏览器兼容性  "esnext"|"modules"
      outDir: "dist", // 指定输出路径
      assetsDir: "assets", // 指定生成静态资源的存放路径   
      manifest: false, // 当设置为 true，构建后将会生成 manifest.json 文件
      minify: "terser", // boolean | 'terser' | 'esbuild' //terser 构建后文件体积更小
      brotliSize: true, //启用/禁用 brotli 压缩大小报告
      cssCodeSplit: true, //启用/禁用 CSS 代码拆分
      sourcemap: false, //构建后是否生成 source map 文件
      emptyOutDir: true, // 在构建之前清空输出目录  
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
      rollupOptions: {
        output: {
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId
              ? chunkInfo.facadeModuleId.split("/")
              : [];
            const fileName =
              facadeModuleId[facadeModuleId.length - 2] || "[name]";
            return `assets/chunk/js/${fileName}/[name].[hash].js`;
          },
          manualChunks: {
            "vue-vendor": ["vue", "vue-router", "pinia"],
            "element-plus": ["element-plus"],
            "utils": ["axios"],
            "echarts-vendor": ["echarts"]
          }
        },
        onwarn(warning, warn) {
          // 忽略 SignalR 的注释警告
          if (warning.code === "INVALID_ANNOTATION") {return;}
          warn(warning);
        }
      },
      // 启用资源分割
      assetsInlineLimit: 4096, //小于4kb的文件会被转为base64
      chunkSizeLimit: 5000, // 设置 chunk 大小的限制为 5000 KiB
      chunkSizeWarningLimit: 1500, // 设置 chunk 大小警告的限制为 1500 KiB
    },
    plugins: [
      vue(),
      createSvgIconsPlugin ({
        iconDirs: [path.resolve(__dirname, "src/assets/svg")],
        symbolId: "icon-[dir]-[name]",
      }),
      !isProd && eslint({
        include: ["src/**/*.js", "src/**/*.vue", "src/*.js", "src/*.vue"],
        cache: false,
      }),
      AutoImport({
        imports: ["vue", "vue-router", "pinia"],
        resolvers: [ElementPlusResolver()]
      }),
      Components({
        resolvers: [ElementPlusResolver({
          importStyle: "sass"
        })]
      }),
    ].filter(Boolean),
  };
});
