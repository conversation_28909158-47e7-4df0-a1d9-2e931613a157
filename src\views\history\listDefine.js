import { getRobot, getScript, getScriptGroup, getUser, getUserGroup } from "@/api/history";

const selectOption = [
  { label: "机器人历史", value: "robot" },
  { label: "剧本历史", value: "script" },
  { label: "剧本组历史", value: "scriptGroup" },
  { label: "用户历史", value: "user" },
  { label: "用户组历史", value: "userGroup" },
];

const tableCol = {
  robot: [
    { prop: "robotName", label: "机器人名称" },
    { prop: "robotId", label: "机器人编号" },
    { prop: "eqpId", label: "设备编号" },
    { prop: "eqpName", label: "设备名称" },
    { prop: "kvmIp", label: "KVM IP" },
    { prop: "kvmPort", label: "KVM Port" },
    { prop: "operator", label: "操作人" },
    { prop: "historyType", label: "操作记录", render: ({ row }) => 
      h("span", 
        row.historyType === 1 ? "增加" : 
          row.historyType === 2 ? "删除" : "修改"
      )
    },
    { prop: "createTime", label: "创建时间", type: "date", width: "150" },
  ],
  script: [
    { prop: "scriptId", label: "剧本编号" },
    { prop: "scriptName", label: "剧本名称" },
    { prop: "scriptVersion", label: "剧本版本号" },
    { prop: "scriptOwner", label: "剧本所有者" },
    { prop: "operator", label: "操作人" },
    { prop: "historyType", label: "操作记录", render: ({ row }) => 
      h("span", 
        row.historyType === 1 ? "增加" : 
          row.historyType === 2 ? "删除" : "修改"
      )
    },
    { prop: "createTime", label: "创建时间", type: "date", width: "150" }
  ],
  scriptGroup: [
    { prop: "scriptGroupName", label: "剧本组名称" },
    { prop: "scriptGroupId", label: "剧本组编号" },
    { prop: "operator", label: "操作人" },
    { prop: "historyType", label: "操作记录", render: ({ row }) => 
      h("span", 
        row.historyType === 1 ? "增加" : 
          row.historyType === 2 ? "删除" : "修改"
      )
    },
    { prop: "createTime", label: "创建时间", type: "date", width: "150" }
  ],
  user: [
    { prop: "userName", label: "用户名" },
    { prop: "phoneNumber", label: "手机号" },
    { prop: "email", label: "邮箱" },
    { prop: "operator", label: "操作人" },
    { prop: "historyType", label: "操作记录", render: ({ row }) => 
      h("span", 
        row.historyType === 1 ? "增加" : 
          row.historyType === 2 ? "删除" : "修改"
      )
    },
    { prop: "createTime", label: "创建时间", type: "date", width: "150" },
  ],
  userGroup: [
    { prop: "name", label: "用户组名" },
    { prop: "operator", label: "操作人" },
    { prop: "historyType", label: "操作记录", render: ({ row }) => 
      h("span", 
        row.historyType === 1 ? "增加" : 
          row.historyType === 2 ? "删除" : "修改"
      )
    },
    { prop: "createTime", label: "创建时间", type: "date", width: "150" },
  ],
};

const apiRequest = {
  robot: getRobot,
  script: getScript,
  scriptGroup: getScriptGroup,
  user: getUser,
  userGroup: getUserGroup
};

export default {
  selectOption,
  tableCol,
  apiRequest
};