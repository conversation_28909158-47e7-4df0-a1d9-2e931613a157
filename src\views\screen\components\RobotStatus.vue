<template>
  <div class="screen-card status-card">
    <div class="card-header">
      <span class="card-title">各状态对应机器人数量统计</span>
    </div>
    <div class="status-grid">
      <div v-for="(value, key) in statusData" :key="key" :class="['status-item', `status-${key.toLowerCase()}`]">
        <div class="status-label">
          {{ key.toUpperCase() }}
        </div>
        <div class="status-value">
          {{ value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getRobotNum } from "@/api/screen";

const statusData = ref({
  RUN: 0,
  IDLE: 0,
  STOP: 0,
  ERROR: 0,
  UNKNOWN: 0
});

const getRobotStatus = async () => {
  const res = await getRobotNum();
  res.data?.forEach(val => {
    statusData.value[val.robotState] = val.robotNum;
  });
};

onMounted(() => {
  getRobotStatus();
});

</script>

<style lang="scss" scoped>
@import url("./index.scss");

.status-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 10px;
  padding: 10px;
  height: calc(100% - 40px);
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 5px;
}

.status-label {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
}

.status-run {
  background-color: rgba(103, 194, 58, 0.2);
  color: #67C23A;
}

.status-idle {
  background-color: rgba(64, 158, 255, 0.2);
  color: #409EFF;
}

.status-stop {
  background-color: rgba(230, 162, 60, 0.2);
  color: #E6A23C;
}

.status-error {
  background-color: rgba(245, 108, 108, 0.2);
  color: #F56C6C;
}

.status-unknown {
  background-color: rgba(144, 147, 153, 0.2);
  color: #909399;
}
</style>