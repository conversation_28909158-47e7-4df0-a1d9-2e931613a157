// import { ElTag } from "element-plus";

const formFiled = [
  {
    type: "input",
    label: "关键词",
    prop: "keywords"
  }
];

const formOption = {
  justify: "center",
  labelWidth: "60px",
  inline: true,
  labelPosition: "right",
  submitButtonText: "查询",
  showCancelButton: false
};

const formData = {
  keywords: ""
};

const tableCol_set = [
  { prop: "scriptId", label: "剧本编号" },
  { prop: "scriptName", label: "剧本名称" },
  { prop: "scriptVersion", label: "剧本版本号" },
  { prop: "groupsName", label: "剧本群组" },
  /* { prop: "isActive", label: "是否启用", width: "80", render: ({ row }) => 
    h(ElTag, { type: row.isActive ? "success" : "info", effect: "dark" }, 
      () => row.isActive ? "是" : "否") }, */
  { prop: "notes", label: "备注" },
  { prop: "createTime", label: "创建时间", type: "date", width: "150" },
  { prop: "updateTime", label: "更新时间", type: "date", width: "150" },
  {
    width: "130",
    label: "操作",
    buttons: [
      { name: "编辑", command: "edit",
        show: (row) => row.status !== 1,
        //disabled: (row) => row.isLocked 
      },
      { name: "删除", type: "danger", command: "delete" }
    ]
  }
];

const tableCol_group = [
  { prop: "scriptGroupId", label: "剧本组编号" },
  { prop: "scriptGroupName", label: "剧本组名称" },
  { prop: "rolesName", label: "绑定用户组" },
  /* { prop: "isActive", label: "是否启用", width: "80", render: ({ row }) => 
    h(ElTag, { type: row.isActive ? "success" : "info", effect: "dark" }, 
      () => row.isActive ? "是" : "否") }, */
  { prop: "notes", label: "备注" },
  { prop: "createTime", label: "创建时间", type: "date", width: "150" },
  { prop: "updateTime", label: "更新时间", type: "date", width: "150" },
  {
    width: "220",
    label: "操作",
    buttons: [
      { name: "编辑", command: "edit",
        show: (row) => row.status !== 1,
        //disabled: (row) => row.isLocked 
      },
      { name: "绑定用户组", type: "info", command: "bind" },
      { name: "删除", type: "danger", command: "delete" }
    ]
  }
];

export default {
  formFiled,
  formOption,
  formData,
  tableCol_set,
  tableCol_group
};