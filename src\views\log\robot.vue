<template>
  <div class="app-container">
    <div style="text-align: center;">
      <el-form ref="formRef" inline :model="formData">
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="formData.keywords" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="时间" prop="dateRange">
          <el-date-picker
            v-model="formData.dateRange" 
            :placeholder="'请选择'"
            clearable
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :format="'YYYY-MM-DD'"
            :value-format="'YYYY-MM-DD'"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">
            查询
          </el-button>
          <el-button type="info" plain @click="resetForm(formRef)">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <EasyTable
      :loading="loading"
      :columns="tableColumn"
      :table-data="listData"
      :options="{
        showPagination: true
      }"
      :pagination="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { useTable } from "@/composables/useTable.js";
import tableDefine from "./tableDefine";

const route = useRoute();
const tableColumn = tableDefine.tableCol_robot;
const listData = ref([]);
const formRef = ref(null);
const formData = reactive({
  keywords: route.query.keywords || "",
  dateRange: ""
});
if(route.query.keywords) {
  formData.keywords = route.query.keywords;
}
const commonFn = inject("$commonUtils"); // 注入公共方法

const { 
  searchParam, 
  pagination, 
  tableData, 
  loading, 
  search, 
  reset, 
  handleSizeChange, 
  handleCurrentChange 
} = useTable({
  api: "/api/app/robot/robot-state-history-list",
  isPageable: true,
  manual: true,
  paramMapping: {
    current: "skipCount",
    pageSize: "maxResultCount",
    dateRange: {
      start: "startTime",
      end: "endTime"
    }
  },
  params: { keywords: formData.keywords }
});

watch(tableData, (newValue) => {
  listData.value = newValue;
}, { immediate: true });

const onSubmit = () => {
  if(formData.dateRange?.length > 0) {
    formData.dateRange = commonFn.formatDateRange(formData.dateRange);
  }
  searchParam.value = { ...formData };
  search();
};

const resetForm = (formEl) => {
  if (!formEl) {return;}
  formEl.resetFields();
  formData.keywords = "";
  reset();
};

</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-weight: 600;
}
:deep(.el-tag--dark){
  &.el-tag--primary{
    background-color: #fadb14;
    border-color: #fadb14;
  }

  &.el-tag--success{
    background-color: #52c41a;
    border-color: #52c41a;
  }

  &.el-tag--danger{
    background-color: #f5222d;
    border-color: #f5222d;
  }

  &.el-tag--warning{
    background-color: #00fefe;
    border-color: #00fefe;
  }

  &.el-tag--info{
    background-color: #a5a5a5;
    border-color: #a5a5a5;
  }

}
</style>