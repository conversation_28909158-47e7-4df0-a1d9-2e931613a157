<template>
  <div>
    <el-table
      v-loading="loading"
      :data="menuList"
      row-key="id"
      border
      :default-expand-all="true"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :row-class-name="tableRowClassName"
    >
      <el-table-column prop="name" label="菜单名称">
        <template #default="{ row }">
          <span :class="{'root-menu': !row.parentId || row.parentId === '0' || row.parentId === 0}">
            {{ row.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="url" label="路由路径" align="center" />
      <el-table-column prop="icon" label="图标" align="center" />
      <el-table-column prop="no" label="排序" width="80" align="center" />
      <el-table-column prop="isVisible" label="显示" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.isVisible ? 'success' : 'info'">
            {{ row.isVisible ? '显示' : '隐藏' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="350" align="center">
        <template #default="{ row }">
          <el-button link type="primary" @click.stop="handleAdd(row)">
            添加子菜单
          </el-button>
          <el-button link type="primary" @click.stop="handleEdit(row)">
            编辑
          </el-button>
          <el-button link type="danger" @click.stop="handleDelete(row)">
            删除
          </el-button>
          <el-button link type="info" @click.stop="handleMove(row, 'up')">
            上移
          </el-button>
          <el-button link type="info" @click.stop="handleMove(row, 'down')">
            下移
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";

defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  menuList: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(["add", "edit", "delete", "move"]);

// 新增子菜单
const handleAdd = (row) => {
  emit("add", row);
};

// 编辑菜单
const handleEdit = (row) => {
  emit("edit", row);
};

// 删除菜单
const handleDelete = (row) => {
  ElMessageBox.confirm("确定要删除此菜单吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    emit("delete", row);
  }).catch(() => {
    // 取消删除
  });
};

// 上移/下移菜单
const handleMove = (row, direction) => {
  emit("move", { row, direction });
};

// 设置行的class
const tableRowClassName = ({ row }) => {
  return !row.parentId || row.parentId === "0" || row.parentId === 0 ? "root-row" : "child-row";
};
</script>

<style scoped>
.root-menu {
  font-weight: bold;
  color: #409EFF;
}

:deep(.root-row) {
  background-color: #f5f7fa !important;
}

:deep(.child-row) {
  background-color: #ffffff;
}

:deep(.el-table__row:hover) {
  background-color: #ecf5ff !important;
}
</style>