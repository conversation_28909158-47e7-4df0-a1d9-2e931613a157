const formFiled = [
  {
    type: "input",
    label: "机器人编号",
    prop: "robotId",
    placeholder: "请填写机器人编号",
    rules: [
      { required: true, message: "请填写机器人编号", trigger: "blur" },
    ],
    readonly: false,
  },
  {
    type: "input",
    label: "机器人名称",
    prop: "robotName",
    placeholder: "请填写机器人名称",
    rules: [
      { required: true, message: "请填写机器人名称", trigger: "blur" },
    ],
  },
  {
    type: "input",
    label: "设备编号",
    prop: "eqpId",
    placeholder: "请填写设备编号",
    rules: [
      { required: true, message: "请填写设备编号", trigger: "blur" },
    ],
  },
  {
    type: "input",
    label: "设备名称",
    prop: "eqpName",
    placeholder: "请填写设备名称",
    rules: [
      { required: true, message: "请填写设备名称", trigger: "blur" },
    ],
  },
  {
    type: "input",
    label: "KVM IP",
    prop: "kvmIp",
    placeholder: "请填写KVM IP",
    rules: [
      { required: true, message: "请填写KVM IP", trigger: "blur" },
    ],
  },
  {
    type: "input",
    label: "KVM Port",
    prop: "kvmPort",
    placeholder: "请填写KVM Port",
    rules: [
      { required: true, message: "请填写KVM Port", trigger: "blur" },
    ],
  },
  {
    type: "radio",
    label: "激活状态",
    prop: "isActive",
    radioList: [
      { label: "是", value: true},
      { label: "否", value: false},
    ],
    rules: [
      { required: true, message: "请选择", trigger: "change" },
    ],
  },
  {
    type: "input",
    label: "备注",
    prop: "notes",
    inputType: "textarea",
    placeholder: "请填写备注",
  }
];

const formOption = {
  justify: "center",
  labelWidth: "100px",
  showResetButton: false
};

const formData = {
  robotName: "",
  eqpId: "",
  eqpName: "",
  isActive: "",
  kvmIp: "",
  notes: ""
};

export default {
  formFiled,
  formOption,
  formData
};