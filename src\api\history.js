import request from "@/utils/request";

export function getRobot(params) {
  return request({
    url: "/api/app/robot/robot-history-list",
    method: "get",
    params,
  });
}

export function getScript(params) {
  return request({
    url: "/api/app/script/script-history-list",
    method: "get",
    params,
  });
}

export function getScriptGroup(params) {
  return request({
    url: "/api/app/script/script-group-history-list",
    method: "get",
    params,
  });
}

export function getUser(params) {
  return request({
    url: "/api/app/user/user-history-list",
    method: "get",
    params,
  });
}

export function getUserGroup(params) {
  return request({
    url: "/api/app/user-group/user-group-history-list",
    method: "get",
    params,
  });
}