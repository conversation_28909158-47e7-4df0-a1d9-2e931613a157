.screen-card {
  background-color: #263445;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: calc((100vh - 120px) / 3);
}

.robot-list-card {
  height: calc((100vh - 120px) / 3 * 2);
}

.card-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background-color: #1f2d3d;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #bfcbd9;
}

.chart-container {
  flex: 1;
  width: 100%;
  height: calc(100% - 40px);
}

.time-filter {
  display: flex;
  align-items: center;
}