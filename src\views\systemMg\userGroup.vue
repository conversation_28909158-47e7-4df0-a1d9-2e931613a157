<template>
  <div class="app-container">
    <EasyForm
      style="text-align: center;"
      :filed-list="formFileds"
      :form-data="formDatas"
      :options="formOptions"
      @submit="searchFn"
      @reset="reset"
    />
    <div class="op-wrapper" style="margin-bottom: 20px;">
      <el-button type="primary" @click="addScript">
        增加
      </el-button>
    </div>
    <EasyTable
      :loading="loading" 
      :columns="tableColumn" 
      :table-data="listData" 
      :options="{ showPagination: true }" 
      :pagination="pagination" 
      @command="TableAction" 
      @size-change="handleSizeChange" 
      @current-change="handleCurrentChange"
    />
    <FormDialog
      ref="fromDialog"
      :dialog-type="scriptFormType"
      :dialog-data="scriptFormData"
      @refresh="search"
    />
    <BindMenu ref="bindMenu" />
  </div>
</template>

<script setup>
import { useTable } from "@/composables/useTable.js";
import listDefine from "./listDefine";
import FormDialog from "./components/userGroupDialog.vue";
// eslint-disable-next-line no-unused-vars
import { ElMessage, ElMessageBox } from "element-plus";
import BindMenu from "./components/bindMenu.vue";
import { deleteUserGroup } from "@/api/systemMg.js";

const formFileds = listDefine.formFiled;
const formOptions = listDefine.formOption;
const formDatas = ref(listDefine.formData);
const fromDialog = ref(null);
const scriptFormType = ref("add");
const scriptFormData = ref({});
const bindMenu = ref(null);

const tableColumn = listDefine.tableCol_group;
const listData = ref([]);

const { 
  searchParam, 
  pagination, 
  tableData, 
  loading, 
  search, 
  reset, 
  handleSizeChange, 
  handleCurrentChange 
} = useTable({
  api: "/api/app/user-group/user-groups",
  isPageable: true,
  manual: true,
  paramMapping: {
    current: "skipCount",
    pageSize: "maxResultCount"
  }
});

watch(tableData, (newValue) => {
  listData.value = newValue;
}, { immediate: true });

const TableAction = (command, row) => {
  switch (command) {
  case "edit":
    scriptFormType.value = "edit";
    scriptFormData.value = row;
    nextTick(() => {
      fromDialog.value.openFormDialog();
    });
    break;
  case "menu":
    bindMenu.value.open(row.id);
    break;
  case "delete":
    ElMessageBox.confirm("是否确认删除?", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }).then(async () => {
      try {
        const res = await deleteUserGroup(row.id);
        if (res) {
          ElMessage.success("删除成功");
          search();
        } else {
          ElMessage.error("删除失败");
        }
      } catch (error) {
        ElMessage.error("删除失败: " + (error.message || "未知错误"));
      }
    });
    break;
  default:
    break;
  }
};

const searchFn = () => {
  searchParam.value = { ...formDatas.value };
  search();
};

const addScript = () => {
  scriptFormType.value = "add";
  scriptFormData.value = {};
  nextTick(() => {
    fromDialog.value.openFormDialog();
  });
};
</script>

<style lang="scss" scoped>

</style>