# RPA 管理系统

## 项目介绍
本项目是基于 Vue 3 + Vite 开发的RPA流程自动化管理系统前端项目，提供流程管理、任务调度、监控分析等功能。

## 技术栈
- Vue 3
- Vite
- JavaScript
- Element Plus
- Pinia
- Vue Router

## 主要功能
- RPA流程管理
- 任务调度中心
- 机器人管理
- 执行日志
- 系统监控
- 权限管理

## 开发环境
```bash
Node.js >= 16
pnpm >= 7
```

## 安装与运行
```bash
# 安装依赖
pnpm install

# 开发环境运行
pnpm dev

# 构建生产环境
pnpm build
```

## 目录结构
```
├── src/
│   ├── api/          # API接口
│   ├── assets/       # 静态资源
│   ├── components/   # 公共组件
│   ├── layouts/      # 布局组件
│   ├── router/       # 路由配置
│   ├── stores/       # 状态管理
│   └── views/        # 页面视图
```
