<template>
  <svg-icon 
    v-if="isSvgIcon" 
    :name="iconName"
    width="18px"
    height="18px"
    class="menu-icon"
  />
  
  <el-icon v-else-if="isElementIcon">
    <component :is="iconName" />
  </el-icon>
</template>

<script setup>
import SvgIcon from "@/components/SvgIcon/index.vue";

const props = defineProps({
  icon: {
    type: String,
    default: ""
  }
});

const { isSvgIcon, isElementIcon, iconName } = computed(() => {
  if (!props.icon) {
    return { isSvgIcon: false, isElementIcon: false, iconName: "" };
  }
  
  // 如果以svg-开头，视为SVG图标
  if (props.icon.startsWith("svg-")) {
    return { 
      isSvgIcon: true, 
      isElementIcon: false, 
      iconName: props.icon.slice(4) // 移除svg-前缀
    };
  }
  
  return { 
    isSvgIcon: false, 
    isElementIcon: true, 
    iconName: props.icon 
  };
}).value;
</script> 

<style scoped>
.menu-icon {
    margin-left: 4px;
    margin-right: 8px;
}
</style>