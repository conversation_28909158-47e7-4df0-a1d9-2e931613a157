<template>
  <div class="screen-card error-trend-card">
    <div class="card-header">
      <span class="card-title">故障趋势统计</span>
    </div>
    <div ref="chartRef" class="chart-container" />
  </div>
</template>

<script setup>
import { useEcharts } from "@/composables/useEcharts";
import { getErrorOptions } from "../config/chartOptions";
import { getErrorNum } from "@/api/screen";

const { chartRef, setOptions } = useEcharts();
const options = getErrorOptions();

// 获取任务数量
const getErrorNumData = async () => {
  const res = await getErrorNum({ periodType: 2 });
  const dataX = [], dataY = [];
  for (let i = 0; i < res.data.length; i++) {
    dataX.push((res.data[i].day).slice(0, 10));
    dataY.push(res.data[i].errorNum);
  }
  options.xAxis.data = dataX;
  options.series[0].data = dataY;
  setOptions(options);
};


onMounted(() => {
  getErrorNumData();
});
</script>

<style lang="scss" scoped>
@import url("./index.scss");
</style>