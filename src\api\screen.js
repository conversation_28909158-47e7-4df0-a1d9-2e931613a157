import request from "@/utils/request";

// 机器人数量统计
export function getRobotNum(params) {
  return request({
    url: "/api/app/robot-monitoring/each-state-robot-num",
    method: "get",
    params,
  });
}

// 机器人状态时间占比
export function getStateTime(params) {
  return request({
    url: "/api/app/robot-monitoring/each-state-accum-time",
    method: "get",
    params,
  });
}

// 任务趋势统计
export function getTaskNum(params) {
  return request({
    url: "/api/app/robot-monitoring/period-robot-task-num",
    method: "get",
    params,
  });
}

// 活跃情况统计
export function getActivity(params) {
  return request({
    url: "/api/app/robot-monitoring/period-robot-activity",
    method: "get",
    params,
  });
}

// 故障趋势统计
export function getErrorNum(params) {
  return request({
    url: "/api/app/robot-monitoring/period-robot-error-num",
    method: "get",
    params,
  });
}