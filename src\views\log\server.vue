<template>
  <div class="app-container">
    <div style="text-align: center;">
      <el-form ref="formRef" inline :model="formData">
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="formData.keywords" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="时间" prop="dateRange">
          <el-date-picker
            v-model="formData.dateRange" 
            :placeholder="'请选择'"
            clearable
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :format="'YYYY-MM-DD'"
            :value-format="'YYYY-MM-DD'"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">
            查询
          </el-button>
          <el-button type="info" plain @click="resetForm(formRef)">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <EasyTable
      :loading="loading"
      :columns="tableColumn"
      :table-data="listData"
      :options="{
        showPagination: true
      }"
      :pagination="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { useTable } from "@/composables/useTable.js";
import tableDefine from "./tableDefine";

const tableColumn = tableDefine.tableCol_server;
const listData = ref([]);
const formRef = ref(null);
const formData = reactive({
  keywords: "",
  dateRange: ""
});
const commonFn = inject("$commonUtils"); // 注入公共方法

const { 
  searchParam, 
  pagination, 
  tableData, 
  loading, 
  search, 
  reset, 
  handleSizeChange, 
  handleCurrentChange 
} = useTable({
  api: "/api/app/robot/robot-server-log-list",
  isPageable: true,
  manual: true,
  paramMapping: {
    current: "skipCount",
    pageSize: "maxResultCount",
    dateRange: {
      start: "startTime",
      end: "endTime"
    }
  }
});

watch(tableData, (newValue) => {
  listData.value = newValue;
}, { immediate: true });

const onSubmit = () => {
  if(formData.dateRange?.length > 0) {
    formData.dateRange = commonFn.formatDateRange(formData.dateRange);
  }
  searchParam.value = { ...formData };
  search();
};

const resetForm = (formEl) => {
  if (!formEl) {return;}
  formEl.resetFields();
  reset();
};
</script>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-weight: 600;
}
</style>