/**
 * @description table 页面表格操作方法封装
 * @param {TableConfig} config - 表格配置
 */
import { reactive, onMounted, computed, toRefs } from "vue";
import request from "@/utils/request"; // 导入你封装的axios实例

/**
 * @typedef {Object} TableConfig
 * @property {string} api - 接口地址
 * @property {boolean} [isPageable=true] - 是否分页
 * @property {boolean} [manual=false] - 是否手动触发
 * @property {Function} [dataCallBack] - 数据处理回调
 * @property {Object} [paramMapping] - 参数映射
 * @property {Object} [params] - 初始化参数
 * @property {Object} [pagination] - 外部分页对象
 */

export const useTable = (config) => {
  const {
    api,
    isPageable = true,
    manual = false,
    dataCallBack,
    paramMapping = {
      current: "skipCount",
      pageSize: "maxResultCount",
      dateRange: {
        start: "startTime",
        end: "endTime"
      }
    },
    params = {},
    pagination: externalPagination = null
  } = config;

  const state = reactive({
    tableData: [],
    pagination: externalPagination || {
      pageSize: 10,
      currentPage: 1,
      total: 0
    },
    searchParam: params,
    totalParam: {},
    loading: false
  });
  
  const pageParams = computed(() => ({
    [paramMapping.current]: (state.pagination.currentPage - 1) * state.pagination.pageSize,
    [paramMapping.pageSize]: state.pagination.pageSize
  }));

  const formatParams = (params) => {
    const formattedParams = { ...params };
    
    // 处理日期范围
    if (params.dateRange && Array.isArray(params.dateRange) && params.dateRange.length === 2) {
      const [start, end] = params.dateRange;
      
      if (start) {
        formattedParams[paramMapping.dateRange.start] = start;
      }
      if (end) {
        formattedParams[paramMapping.dateRange.end] = end;
      }
      
      delete formattedParams.dateRange;
    }

    return formattedParams;
  };

  const updateTotalParams = () => {
    state.totalParam = {};
    
    const nowSearchParam = Object.entries(state.searchParam)
      // eslint-disable-next-line no-unused-vars
      .filter(([_, value]) => value || value === false || value === 0)
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    const formattedParams = formatParams(nowSearchParam);
    
    Object.assign(state.totalParam, formattedParams, isPageable ? pageParams.value : {});
  };

  const getTableData = async () => {
    try {
      state.loading = true;
      updateTotalParams();
      // 使用你封装的axios实例发送请求
      const res = await request({
        url: api,
        method: "GET",
        params: state.totalParam,
        showLoading: false // 是否显示全局loading，根据需求设置
      });

      if (res.code !== 200) {
        throw new Error(res.msg || res.message || "请求处理失败");
      }

      const { totalCount, items } = res.data || { totalCount: 0, items: [] };
      const processedData = dataCallBack ? dataCallBack(items) : items;
      state.tableData = processedData;
      
      if (isPageable) {
        updatePagination({
          currentPage: state.pagination.currentPage,
          pageSize: state.pagination.pageSize,
          total: totalCount
        });
      }
    } catch (error) {
      console.error("获取表格数据失败:", error);
      state.tableData = [];
      updatePagination({ total: 0 });
    } finally {
      state.loading = false;
    }
  };

  const updatePagination = (obj) => {
    if (externalPagination) {
      Object.assign(externalPagination, obj);
    } else {
      Object.assign(state.pagination, obj);
    }
  };

  const search = () => {
    state.pagination.currentPage = 1;
    getTableData();
  };

  const reset = () => {
    state.searchParam = {};
    state.totalParam = {};
    state.pagination = {
      pageSize: 10,
      currentPage: 1,
      total: 0
    };
    getTableData();
  };

  const handleSizeChange = (val) => {
    state.pagination.currentPage = 1;
    state.pagination.pageSize = val;
    getTableData();
  };

  const handleCurrentChange = (val) => {
    state.pagination.currentPage = val;
    getTableData();
  };

  onMounted(() => {
    if (manual) {
      getTableData();
    }
  });

  return {
    ...toRefs(state),
    getTableData,
    handleCurrentChange,
    handleSizeChange,
    search,
    reset
  };
};