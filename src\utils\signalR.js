import { HubConnectionBuilder, LogLevel } from "@microsoft/signalr";
import * as signalR from "@microsoft/signalr";

class SignalRService {
  constructor() {
    this.connection = null;
    this.handlers = new Map(); // 使用Map来存储每个事件名称及其对应的处理器集合
    this.isConnecting = false; // 连接状态标志，防止重复连接
    this.userCount = 0; // 使用计数器跟踪使用SignalR的组件数量
    this.connectionPromise = null; // 存储连接Promise以避免并发调用
  }

  // 初始化连接
  async initConnection(hubUrl) {
    this.userCount++;
    console.log("[SignalR] 初始化连接...");

    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    if (this.connection?.state === "Connected") {
      console.log("[SignalR] 已经连接");
      return Promise.resolve();
    }

    this.connection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        skipNegotiation: true,
        transport: signalR.HttpTransportType.WebSockets
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect({
        nextRetry: (retryContext) => {
          if (retryContext.elapsedMilliseconds < 300000) { // 5分钟内尝试重连
            return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000);
          }
          return null; // 超过5分钟停止重试
        }
      })
      .build();

    this.connection.onreconnecting((error) => {
      console.log("[SignalR] 正在重新连接...", error);
      this.isConnecting = true;
    });

    this.connection.onreconnected((connectionId) => {
      console.log("[SignalR] 重新连接成功", connectionId);
      this.isConnecting = false;
      this.reregisterAllHandlers();
    });

    this.connection.onclose((error) => {
      console.log("[SignalR] 连接关闭", error);
      
      this.connectionPromise = null;
      
      this.isConnecting = false;
      // 如果连接被意外关闭，尝试重新启动连接
      if (this.connection && !this.connectionPromise) {
        console.log("[SignalR] 尝试重新建立连接...");
        this.connectionPromise = this.startConnection();
      }
    });

    this.connectionPromise = this.startConnection();
    
    try {
      await this.connectionPromise;
      return this.connectionPromise;
    } finally {
      this.connectionPromise = null;
    }
  }

  // 启动连接
  async startConnection() {
    if (this.isConnecting) {
      return;
    }

    if (!this.connection) {
      return Promise.resolve();
    }

    this.isConnecting = true;
    try {
      await this.connection.start();
      console.log("[SignalR] 连接成功");
    } catch (err) {
      console.error("[SignalR] 连接失败:", err);
      if (err.statusCode === 401) {
        console.error("[SignalR] 未授权访问");
      }
      if (this.connection) {
        setTimeout(() => this.startConnection(), 5000);
      }
    } finally {
      this.isConnecting = false;
    }
  }

  // 注册事件处理器时增加防重复机制
  registerEventHandler(eventName, callback) {
    if (!this.connection) {
      console.warn(`[SignalR] 尝试注册 ${eventName} 事件处理器时连接不存在`);
      return;
    }

    // 先移除已存在的相同回调，避免重复
    this.unregisterEventHandler(eventName, callback);

    if (!this.handlers.has(eventName)) {
      this.handlers.set(eventName, new Set());
    }

    const handlers = this.handlers.get(eventName);
    handlers.add(callback);
    this.connection.on(eventName, callback);
  }

  // 通用事件移除方法
  unregisterEventHandler(eventName, callback) {
    if (!this.connection) {return;}

    const handlersSet = this.handlers.get(eventName);
    if (handlersSet?.has(callback)) {
      handlersSet.delete(callback);
      this.connection.off(eventName, callback);
    }
  }

  // 注册事件处理器
  onReceiveUpdate(callback) {
    this.registerEventHandler("robotstatuschangedevent", callback);
  }

  // 移除事件处理器
  offReceiveUpdate(callback) {
    this.unregisterEventHandler("robotstatuschangedevent", callback);
  }

  // 重新注册所有处理器
  reregisterAllHandlers() {
    if (!this.connection) {return;}

    for (const [eventName, handlersSet] of this.handlers.entries()) {
      handlersSet.forEach(handler => {
        this.connection.on(eventName, handler);
      });
    }
  }

  // 停止连接
  async stopConnection() {
    this.userCount--;

    if (this.userCount <= 0) {
      console.log("[SignalR] 停止所有连接");
      this.userCount = 0;
      
      const currentConnection = this.connection;
      this.connection = null;
      this.connectionPromise = null;
      
      if (currentConnection) {
        // 先清理所有处理器
        this.clearHandlers();
        
        try {
          await currentConnection.stop();
          currentConnection.onclose = null;
          currentConnection.onreconnecting = null;
          currentConnection.onreconnected = null;
        } catch (error) {
          console.warn("[SignalR] 停止连接时出错:", error);
        }
      }
      
      // 确保完全重置状态
      this.isConnecting = false;
      this.handlers.clear();
    } else {
      console.log(`[SignalR] 还有 ${this.userCount} 个组件在使用连接，暂不关闭`);
    }
  }

  // 优化清理处理器的方法
  clearHandlers() {
    if (!this.connection) {return;}

    this.handlers.forEach((handlersSet, eventName) => {
      handlersSet.forEach(handler => {
        try {
          this.connection.off(eventName, handler);
        } catch (error) {
          console.warn(`[SignalR] 清理事件处理器失败 ${eventName}:`, error);
        }
      });
      handlersSet.clear();
    });
    this.handlers.clear();
  }
}

export const signalRService = new SignalRService();