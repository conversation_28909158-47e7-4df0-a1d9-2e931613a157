import request from "@/utils/request";
// 机器人监控 —— 获取列表
export function getMonitoringDatas(params) {
  return request({
    url: "/api/app/robot-monitoring/monitoring-datas",
    method: "get",
    params
  });
}
// 机器人监控 —— 获取状态变化
export function getRobotStatusChange(id) {
  return request({
    url: "/api/app/robot-monitoring/robot-state-info/" + id,
    method: "get"
  });
}
// 机器人管理 —— 列表
export function getRobot(params) {
  return request({
    url: "/api/app/robot/robot-list",
    method: "get",
    params
  });
}
// 机器人管理 —— 详情
export function getRobotInfo(id) {
  return request({
    url: "/api/app/robot/robot/" + id,
    method: "get",
  });
}
// 机器人管理 —— 添加
export function addRobot(data) {
  return request({
    url: "/api/app/robot/robot",
    method: "post",
    data
  });
}
// 机器人管理 —— 更新
export function updateRobot(data) {
  return request({
    url: "/api/app/robot/edit-robot",
    method: "post",
    data
  });
}
// 机器人管理 —— 更新
export function deleteRobot(id) {
  return request({
    url: "/api/app/robot/robot/" + id,
    method: "delete"
  });
}