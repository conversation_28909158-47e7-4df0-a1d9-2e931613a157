{"name": "Vue Element UI 开发规范", "version": "1.0.0", "description": "针对Vue 3 + Element Plus的专业开发规则集", "rules": {"element-import-optimization": {"pattern": "import\\s+\\{[^}]*\\}\\s+from\\s+['\"]element-plus['\"]", "message": "建议使用按需导入，避免引入整个Element Plus库影响性能", "suggestion": "import { El<PERSON><PERSON>on, ElInput } from 'element-plus'", "severity": "warning", "fileTypes": [".vue", ".ts", ".js"]}, "vue-element-component-naming": {"pattern": "<el-[a-z-]+(?![^>]*v-bind)", "message": "Element组件建议使用v-bind或props传递动态属性", "severity": "info", "fileTypes": [".vue"]}, "element-form-validation": {"pattern": "<el-form(?![^>]*:model)(?![^>]*ref)", "message": "el-form必须绑定model和ref，确保表单验证功能正常", "severity": "error", "fileTypes": [".vue"]}, "element-table-key-required": {"pattern": "<el-table(?![^>]*row-key)", "message": "el-table建议设置row-key属性提升渲染性能", "severity": "warning", "fileTypes": [".vue"]}, "vue-composition-with-element": {"pattern": "import\\s*\\{[^}]*ref[^}]*\\}\\s*from\\s*['\"]vue['\"]", "requirePattern": "import\\s*\\{[^}]*ElMessage[^}]*\\}", "message": "使用Element组件时建议配合Vue 3 Composition API", "severity": "info", "fileTypes": [".vue"]}, "element-theme-variables": {"pattern": "--el-color-primary:\\s*#[0-9a-fA-F]{6}", "message": "使用Element Plus CSS变量系统进行主题定制", "severity": "info", "fileTypes": [".vue", ".scss", ".css"]}, "element-layout-responsive": {"pattern": "<el-col(?![^>]*:span)(?![^>]*:xs|:sm|:md|:lg|:xl)", "message": "el-col建议设置响应式断点属性，确保移动端适配", "severity": "warning", "fileTypes": [".vue"]}, "element-message-instance": {"pattern": "\\$message\\.|ElMessage\\(", "message": "优先使用ElMessage实例方法而非全局挂载", "severity": "info", "fileTypes": [".vue", ".ts", ".js"]}, "vue-element-async-loading": {"pattern": "<el-table(?![^>]*v-loading)", "message": "数据表格建议添加loading状态提升用户体验", "severity": "warning", "fileTypes": [".vue"]}, "element-config-provider": {"filePattern": "main\\.(ts|js)$", "pattern": "createApp\\(", "requirePattern": "ElConfigProvider", "message": "应用根组件建议使用ElConfigProvider进行全局配置", "severity": "info"}, "vue-element-form-rules": {"pattern": "<el-form-item(?![^>]*prop)", "message": "el-form-item必须设置prop属性才能触发验证", "severity": "error", "fileTypes": [".vue"]}, "element-icon-usage": {"pattern": "<el-icon(?![^>]*><[A-Z])", "message": "el-icon内部应包含具体的图标组件", "severity": "warning", "fileTypes": [".vue"]}, "vue-element-performance": {"pattern": "<el-select(?![^>]*filterable)(?=.*multiple)", "message": "多选el-select建议开启filterable提升用户体验", "severity": "info", "fileTypes": [".vue"]}, "element-dialog-accessibility": {"pattern": "<el-dialog(?![^>]*title)", "message": "el-dialog必须设置title属性确保无障碍访问", "severity": "error", "fileTypes": [".vue"]}, "vue-element-teleport": {"pattern": "<el-(?:dialog|drawer|popover)(?![^>]*append-to-body)", "message": "弹窗类组件建议设置append-to-body避免层级问题", "severity": "warning", "fileTypes": [".vue"]}}, "ruleGroups": {"critical": ["element-form-validation", "vue-element-form-rules", "element-dialog-accessibility"], "performance": ["element-import-optimization", "element-table-key-required", "vue-element-async-loading", "vue-element-performance"], "user-experience": ["element-layout-responsive", "element-message-instance", "vue-element-teleport"], "best-practices": ["vue-composition-with-element", "element-theme-variables", "element-config-provider", "element-icon-usage"]}, "autoFix": {"element-import-optimization": true, "element-theme-variables": true}, "projectSettings": {"elementVersion": "2.x", "vueVersion": "3.x", "typescript": true, "customTheme": true}, "codeTemplates": {"el-form-template": {"description": "标准Element表单模板", "template": "<el-form ref=\"formRef\" :model=\"formData\" :rules=\"rules\">\n  <el-form-item label=\"\" prop=\"\">\n    <el-input v-model=\"formData.field\" />\n  </el-form-item>\n</el-form>"}, "el-table-template": {"description": "标准Element表格模板", "template": "<el-table :data=\"tableData\" row-key=\"id\" v-loading=\"loading\">\n  <el-table-column prop=\"\" label=\"\" />\n</el-table>"}}}