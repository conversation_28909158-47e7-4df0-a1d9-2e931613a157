<template>
  <div class="robot-dialog">
    <dialog-provider ref="formDialogRef">
      <EasyForm
        ref="easyForm"
        :filed-list="formFileds"
        :form-data="formDatas"
        :options="formOptions"
        style="text-align: center;margin: 10px 20px 0;"
        @submit="submitForm"
        @cancel="cancelForm"
        @success="successForm"
      />
    </dialog-provider>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import formDefine from "./formDefine.js";
import { changeUserPsw } from "@/api/systemMg.js";

defineProps({
  dialogData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["refresh"]);

const formDialogRef = ref(null);
const easyForm = ref(null);
const userId = ref("");
const formFileds = ref([
  {
    type: "input",
    label: "密码",
    prop: "password",
    inputType: "password",
    placeholder: "请填写密码",
    rules: [
      { required: true, message: "请填写密码", trigger: "blur" },
      { min: 6, max: 20, message: "密码长度在6到20个字符之间", trigger: "blur" },
      //{ pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[\s\S]{6,20}$/, message: "密码必须包含大小写字母和数字", trigger: "blur" }
    ],
  },
  {
    type: "input",
    label: "确认密码",
    prop: "passwordSure",
    inputType: "password",
    placeholder: "请填写确认密码",
    rules: [
      { required: true, message: "请填写确认密码", trigger: "blur" },
      { validator: (rule, value, callback) => {
        if (value !== formDatas.value.password) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      }, trigger: "blur" }
    ],
  },
]);
const formOptions = formDefine.formOption;
const formDatas = ref({
  password: "",
  passwordSure: ""
});
const isSubmitting = ref(false);

// 打开表单对话框
const openFormDialog = (id) => {
  userId.value = id;
  formDialogRef.value?.open({
    title: "修改密码",
    width: "500px",
    closeOnClickModal: false
  });
};
  
// 取消表单
const cancelForm = () => {
  formDialogRef.value.close();
};
  
// 提交表单
const submitForm = async (data) => {
  if (isSubmitting.value) {return;}
  formDialogRef.value.showLoading();
  console.log(data);
  try {
    isSubmitting.value = true;
    const res = await changeUserPsw(userId.value, {
      psw: data.password
    });
    if (res.code !== 200) {
      ElMessage.error(res.msg);
    } else {
      ElMessage.success("修改密码成功");
    }
    formDialogRef.value.hideLoading();
    formDatas.value = {};
    formDialogRef.value.close();
  } catch (error) {
    console.error("表单提交失败:", error);
  } finally {
    formDialogRef.value.hideLoading();
    isSubmitting.value = false;
  }
};

// 表单提交成功回调
const successForm = () => {
  emit("refresh");
};


defineExpose({
  openFormDialog
});

</script>