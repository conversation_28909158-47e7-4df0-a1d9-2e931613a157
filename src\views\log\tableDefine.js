import { ElTag } from "element-plus";

// 状态映射配置
const stateMap = {
  "ERROR": { type: "danger", label: "ERROR" },
  "RUN": { type: "success", label: "RUN" },
  "IDLE": { type: "primary", label: "IDLE" },
  "STOP": { type: "warning", label: "STOP" },
};

const tableCol_robot = [
  { prop: "robotId", label: "机器人编号" },
  { prop: "robotName", label: "机器人名称" },
  { 
    prop: "robotState", 
    label: "状态",
    render: ({ row }) => {
      const state = stateMap[row.robotState] || { type: "info", label: "UNKNOWN" };
      return h(ElTag, { 
        type: state.type, 
        effect: "dark" 
      }, () => state.label);
    }
  },
  { prop: "createTime", label: "状态更新时间", type: "date" },
];

const tableCol_step = [
  { prop: "robotId", label: "机器人编号" },
  { prop: "robotName", label: "机器人名称" },
  { prop: "robotStep", label: "执行步骤" },
  { prop: "createTime", label: "创建时间", type: "date", width: "150" },
];

const tableCol_server = [
  { prop: "logType", label: "类型", width: "100px" },
  { prop: "logInfo", label: "内容" },
  { prop: "createTime", label: "创建时间", type: "date", width: "250"  },
];

export default {
  tableCol_robot, tableCol_step, tableCol_server
};