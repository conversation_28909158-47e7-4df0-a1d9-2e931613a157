# RPA 管理系统项目规范

## 目录

1. [项目概述](#项目概述)
2. [技术栈](#技术栈)
3. [目录结构规范](#目录结构规范)
4. [命名规范](#命名规范)
5. [代码风格](#代码风格)
6. [组件开发规范](#组件开发规范)
7. [API 接口规范](#api-接口规范)
8. [状态管理规范](#状态管理规范)
9. [路由规范](#路由规范)
10. [提交规范](#提交规范)

## 项目概述

本项目是基于 Vue 3 + Vite + Element Plus 开发的 RPA（机器人流程自动化）管理系统，用于管理和监控 RPA 机器人、脚本和任务执行。

## 技术栈

- **前端框架**：Vue 3.5.x
- **构建工具**：Vite 6.x
- **UI 框架**：Element Plus 2.x
- **状态管理**：Pinia 3.x
- **路由管理**：Vue Router 4.x
- **HTTP 客户端**：Axios
- **实时通信**：SignalR
- **CSS 预处理器**：SCSS
- **代码规范**：ESLint

## 目录结构规范

```
src/
├── api/                # API 接口定义
├── assets/             # 静态资源
│   ├── images/         # 图片资源
│   └── svg/            # SVG 图标
├── components/         # 公共组件
│   ├── Breadcrumb/     # 面包屑导航组件
│   ├── DialogProvider/ # 对话框提供者组件
│   ├── EasyForm/       # 表单组件
│   ├── EasyTable/      # 表格组件
│   └── ...
├── composables/        # 组合式函数
├── directives/         # 自定义指令
├── layout/             # 布局组件
├── router/             # 路由配置
│   └── modules/        # 路由模块
├── store/              # 状态管理
│   └── modules/        # 状态模块
├── styles/             # 全局样式
├── utils/              # 工具函数
└── views/              # 页面视图
    ├── dashboard/      # 仪表盘
    ├── robotMg/        # 机器人管理
    ├── scriptMg/       # 脚本管理
    └── systemMg/       # 系统管理
```

## 命名规范

### 文件命名

- **组件文件**：使用 PascalCase（大驼峰）命名，如 `EasyForm.vue`、`DialogProvider.vue`
- **非组件文件**：使用 camelCase（小驼峰）命名，如 `useDialog.js`、`request.js`
- **目录名**：使用小写字母，多个单词用驼峰命名，如 `robotMg`、`scriptMg`

### 变量命名

- **变量**：使用 camelCase（小驼峰）命名，如 `formData`、`tableList`
- **常量**：使用全大写，单词间用下划线分隔，如 `MAX_COUNT`、`API_URL`
- **组件名**：使用 PascalCase（大驼峰）命名，如 `EasyForm`、`DialogProvider`
- **私有属性/方法**：以下划线开头，如 `_options`、`_handleSubmit`

## 代码风格

### JavaScript/Vue

- 使用双引号（"）而非单引号（'）
- 语句末尾使用分号
- 缩进使用 2 个空格
- 使用 ES6+ 语法特性
- 优先使用组合式 API（`<script setup>`）
- 组件属性顺序：
  1. 指令（v-if/v-for/v-show/v-bind）
  2. 唯一属性（ref/key）
  3. 双向绑定（v-model）
  4. 其他属性
  5. 事件（@click/@change）

### CSS/SCSS

- 使用 SCSS 预处理器
- 类名使用 kebab-case（短横线）命名，如 `easy-form`、`form-item`
- 组件样式使用 `scoped` 属性隔离
- 全局样式放在 `styles` 目录下
- 使用变量管理颜色、字体等通用样式

## 组件开发规范

### 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup>
// 导入语句
import { ... } from '...';

// Props 定义
const props = defineProps({
  propName: {
    type: Type,
    required: boolean,
    default: defaultValue
  }
});

// Emits 定义
const emit = defineEmits(['eventName']);

// 响应式状态
const state = ref(initialValue);

// 计算属性
const computedValue = computed(() => { ... });

// 方法定义
const handleEvent = () => { ... };

// 生命周期钩子
onMounted(() => { ... });

// 对外暴露的属性和方法
defineExpose({
  property: value,
  method: function
});
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

### 表单组件（EasyForm）使用规范

1. **表单字段定义**：使用统一的字段定义格式

```javascript
const formFields = [
  {
    type: "input", // 组件类型
    label: "字段名称",
    prop: "fieldName", // 字段属性名
    placeholder: "请输入",
    rules: [ // 验证规则
      { required: true, message: "必填项", trigger: "blur" }
    ]
  }
];
```

2. **表单数据**：使用响应式对象管理表单数据

```javascript
const formData = ref({
  fieldName: ""
});
```

3. **表单验证**：使用统一的验证方法

```javascript
// 自定义验证器
const validator = (rule, value, callback, source) => {
  // 注意：验证器中应直接使用 source 对象获取表单中的其他字段值
  // 不要使用外部的静态对象
  if (condition) {
    callback(new Error("错误信息"));
  } else {
    callback();
  }
};
```

## API 接口规范

### 接口定义

- 按模块组织接口文件，放在 `api` 目录下
- 使用 RESTful 风格的接口命名
- 使用 Axios 实例进行请求封装

```javascript
import request from '@/utils/request';

// 获取列表
export function getList(params) {
  return request({
    url: '/api/resource',
    method: 'get',
    params
  });
}

// 创建资源
export function createResource(data) {
  return request({
    url: '/api/resource',
    method: 'post',
    data
  });
}
```

## 状态管理规范

- 使用 Pinia 进行状态管理
- 按模块组织状态文件，放在 `store/modules` 目录下
- 遵循 actions、getters、state 的组织结构

```javascript
import { defineStore } from 'pinia';

export const useAppStore = defineStore('app', {
  state: () => ({
    // 状态定义
  }),
  getters: {
    // 计算属性
  },
  actions: {
    // 方法定义
  }
});
```

## 路由规范

- 按模块组织路由文件，放在 `router/modules` 目录下
- 使用懒加载优化性能
- 合理设置路由元信息（meta）

```javascript
const routes = [
  {
    path: '/module',
    component: () => import('@/layout/index.vue'),
    redirect: '/module/index',
    meta: { title: '模块名称', icon: 'icon-name' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/module/index.vue'),
        name: 'ModuleIndex',
        meta: { title: '子页面名称', icon: 'icon-name' }
      }
    ]
  }
];
```

## 提交规范

- 使用语义化的提交信息
- 提交前进行代码格式化和 ESLint 检查
- 提交信息格式：`<type>(<scope>): <subject>`
  - type: feat, fix, docs, style, refactor, test, chore
  - scope: 影响范围（可选）
  - subject: 简短描述

示例：
- `feat(user): 添加用户管理功能`
- `fix(form): 修复表单验证问题`