import { ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts";

/**
 * @typedef {Object} EChartsOptions - ECharts 配置项
 * @typedef {import('echarts').EChartsType} EChartsInstance - ECharts 实例类型
 */

export function useEcharts() {
  const chartRef = ref(null);
  let chartInstance = null;

  const initChart = () => {
    if (!chartRef.value) {return;}
    chartInstance = echarts.init(chartRef.value);
  };

  const setOptions = (options, clear = true) => {
    if (!chartInstance) {
      initChart();
    }
    if (clear) {
      chartInstance?.clear();
    }
    
    chartInstance?.setOption(options);
  };

  const resize = () => {
    chartInstance?.resize();
  };

  const destroyChart = () => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }
  };

  const getInstance = () => chartInstance;

  const handleResize = () => {
    nextTick(() => {
      resize();
    });
  };

  onMounted(() => {
    window.addEventListener("resize", handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
    destroyChart();
  });

  return {
    chartRef,
    echarts,
    setOptions,
    resize,
    getInstance,
    destroyChart,
  };
}
