<template>
  <div class="bind-menu">
    <dialog-provider ref="formDialogRef" title="绑定菜单" width="500px">
      <el-tree
        ref="treeRef"
        :data="menuList"
        :props="defaultProps"
        show-checkbox
        node-key="id"
        :default-expand-all="true"
        :default-checked-keys="checkedKeys"
      />
      <div style="text-align: center; margin-top: 20px;">
        <el-button type="primary" @click="handleSave">
          确 认
        </el-button>
        <el-button @click="formDialogRef.close">
          取 消
        </el-button>
      </div>
    </dialog-provider>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getMenu } from "@/api/systemMg";
import listDefine from "../listDefine";
// eslint-disable-next-line no-unused-vars
import { ElMessage } from "element-plus";
import { bindMenu, getGroupBindMenu } from "@/api/systemMg";

const formDialogRef = ref(null);
const treeRef = ref(null);
const menuList = ref([]);
const roleId = ref("");
const allBindMenu = ref([]);
const checkedKeys = ref([]);

const defaultProps = {
  children: "children",
  label: "name"
};

const getMenuData = async () => {
  try {
    const res = await getMenu();
    menuList.value = listDefine.arrayToTree(res.data.items);
  } catch (error) {
    console.error("获取菜单列表失败:", error);
  }
};

const bindMenuData = async () => {
  try {
    const res = await getGroupBindMenu(roleId.value);
    allBindMenu.value = res.data.items.map(item => item.id);
  } catch (error) {
    console.error("获取角色绑定的菜单失败:", error);
  }
};

// 判断是否为父节点
const isParentNode = (id) => {
  const findNode = (list) => {
    for (const item of list) {
      if (item.id === id) {
        return item.children && item.children.length > 0;
      }
      if (item.children) {
        const found = findNode(item.children);
        if (found !== undefined) {return found;}
      }
    }
  };
  return findNode(menuList.value);
};

const open = async(id) => {
  roleId.value = id;
  await bindMenuData();
  formDialogRef.value?.open();
  treeRef.value?.setCheckedKeys([]);
  const leafNodeIds = allBindMenu.value.filter(id => !isParentNode(id));
  checkedKeys.value = leafNodeIds;
};

const handleSave = async () => {
  const checkedKeys = treeRef.value.getCheckedKeys();
  const halfCheckedKeys = treeRef.value.getHalfCheckedKeys();
  const allSelectedKeys = [...checkedKeys, ...halfCheckedKeys];
  const res = await bindMenu({
    roleId: roleId.value,
    menuIds: allSelectedKeys
  });
  if (res.code !== 200) {
    ElMessage.error(res.msg);
  } else {
    ElMessage.success("修改权限成功");
  }
  formDialogRef.value?.close();
};

onMounted(() => {
  getMenuData();
});

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.bind-menu {
  .el-tree {
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>