import { ref } from "vue";

export default function useElTableScroll(dom, autoScrollFlag = true) {
  const scrollTop = ref(0);
  //内容高度
  const scrollHeight = ref(0);
  //滚动区域
  const scrollConHeight = ref(0);
  //定时器
  const timer = ref(null);
  //定时器
  const timerout = ref(null);
  var requestAnimationFrame = window.requestAnimationFrame || window.mozRequestAnimationFrame ||
    window.webkitRequestAnimationFrame || window.msRequestAnimationFrame;
  var cancelAnimationFrame = window.cancelAnimationFrame || window.mozCancelAnimationFrame;
  //是否自动滚动
  const autoScroll = () => {
    if (!autoScrollFlag) {
      return false;
    }
    timerout.value = setTimeout(() => {
      scrollConHeight.value = dom.value.$refs.bodyWrapper.clientHeight;
      scrollHeight.value = dom.value.$refs.tableBody.clientHeight;
      scrollStep();
    }, 1000);
  };
  //滚动添加
  function scrollStep() {
    scrollTop.value++;
    if (
      scrollTop.value > scrollHeight.value - scrollConHeight.value &&
      scrollHeight.value > 0
    ) {
      timerout.value && clearTimeout(timerout.value);
      setTimeout(() => {
        scrollTop.value = 0;
        dom.value.setScrollTop(scrollTop.value);
        autoScroll();
      }, 1000);
    } else {
      timer.value = requestAnimationFrame(scrollStep);
    }
    dom.value.setScrollTop(scrollTop.value);
  }
  //清除滚动
  function clearScroll() {
    timer.value && cancelAnimationFrame(timer.value);
    timerout.value && clearTimeout(timerout.value);
  }
  //鼠标进入
  function cellMouseEnter() {
    clearScroll();
  }
  //鼠标移出
  function cellMouseLeave() {
    scrollTop.value = dom.value.$el.querySelector(
      ".el-scrollbar__wrap"
    ).scrollTop;
    clearScroll();
    autoScroll();
  }
  return {
    autoScroll,
    clearScroll,
    cellMouseEnter,
    cellMouseLeave
  };
}