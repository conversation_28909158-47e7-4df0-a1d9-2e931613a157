<template>
  <div class="app-container">
    <div style="text-align: center;">
      <el-form ref="formRef" inline :model="formData" :rules="formRules">
        <el-form-item label="类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择" style="width: 150px">
            <el-option
              v-for="item in listDefine.selectOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="formData.keywords" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="时间" prop="dateRange">
          <el-date-picker
            v-model="formData.dateRange" 
            :placeholder="'请选择'"
            clearable
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :format="'YYYY-MM-DD'"
            :value-format="'YYYY-MM-DD'"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(formRef)">
            查询
          </el-button>
          <el-button type="info" plain @click="resetForm(formRef)">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <EasyTable
      :loading="loading"
      :columns="tableColumn"
      :table-data="listData"
      :pagination="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import listDefine from "./listDefine";
const commonFn = inject("$commonUtils");

const formData = reactive({
  type: "robot",
  keywords: "",
  dateRange: ""
});
const formRules = reactive({
  type: [{ required: true, message: "请选择", trigger: "change" }]
});
const formRef = ref(null);
const loading = ref(false);
const tableColumn = ref(listDefine.tableCol[formData.type]);
const listData = ref([]);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

const getList = () => {
  loading.value = true;
  const { currentPage, pageSize } = pagination;
  listDefine.apiRequest[formData.type]({
    keywords: formData.keywords,
    startTime: commonFn.formatDateRange(formData.dateRange)[0],
    endTime: commonFn.formatDateRange(formData.dateRange)[1],
    skipCount: (currentPage - 1) * pageSize,
    maxResultCount: pageSize
  }).then((res) => {
    listData.value = res.data?.items || [];
    pagination.total = res.data?.totalCount || 0;
  }).catch(() => {
    listData.value =  [];
    pagination.total = 0;
  }).finally(() => {
    loading.value = false;
  });
};

const handleCurrentChange = (val) => {
  pagination.currentPage = val;
  getList();
};

const handleSizeChange = (val) => {
  pagination.pageSize = val;
  getList();
};

const onSubmit = () => {
  tableColumn.value = listDefine.tableCol[formData.type];
  pagination.currentPage = 1;
  getList();
};

const resetForm = (formEl) => {
  if (!formEl) {return;}
  formEl.resetFields();
  formData.type = "robot";
  tableColumn.value = listDefine.tableCol[formData.type];
  getList();
};


onMounted(() => {
  getList();
});

</script>

<style lang="scss" scoped>

</style>